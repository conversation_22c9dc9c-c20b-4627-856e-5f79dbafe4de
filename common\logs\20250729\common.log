2025-07-29 17:18:48 - MainProcess - 14536 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:18:50 - SpawnProcess-1 - 19144 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:19:03 - SpawnProcess-1 - 19144 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:19:03 - SpawnProcess-1 - 19144 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:19:03 - SpawnProcess-1 - 19144 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:27] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:37] - 找到目标窗口: 'data:, - Google Chrome'
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:42] - 原始窗口尺寸: 2401 x 1102
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:43] - 原始窗口位置: (11, 11)
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:52] - 目标尺寸: 960 x 1040
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - DEBUG - [window_utils.py:53] - 目标位置: (960, 0)
2025-07-29 17:19:06 - SpawnProcess-1 - 19144 - common - ERROR - [window_utils.py:72] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:21:25 - MainProcess - 38052 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:21:27 - SpawnProcess-1 - 50832 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:21:28 - SpawnProcess-1 - 50832 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:21:28 - SpawnProcess-1 - 50832 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:21:28 - SpawnProcess-1 - 50832 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:27] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:37] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:42] - 原始窗口尺寸: 960 x 472
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:43] - 原始窗口位置: (213, 338)
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:52] - 目标尺寸: 960 x 1040
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - DEBUG - [window_utils.py:53] - 目标位置: (960, 0)
2025-07-29 17:21:30 - SpawnProcess-1 - 50832 - common - ERROR - [window_utils.py:72] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:25:39 - MainProcess - 48120 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:25:40 - SpawnProcess-1 - 45180 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:25:43 - SpawnProcess-1 - 45180 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:25:43 - SpawnProcess-1 - 45180 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:25:43 - SpawnProcess-1 - 45180 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:25:45 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:25:45 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - conda  activate test - python  main.py'
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (224, 224)
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:25:48 - SpawnProcess-1 - 45180 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:28:22 - MainProcess - 58188 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:28:23 - SpawnProcess-1 - 21748 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:28:24 - SpawnProcess-1 - 21748 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:28:24 - SpawnProcess-1 - 21748 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:28:24 - SpawnProcess-1 - 21748 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:28:25 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:28:25 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (96, 96)
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:28:29 - SpawnProcess-1 - 21748 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:30:42 - MainProcess - 59032 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:30:44 - SpawnProcess-1 - 55164 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:30:47 - SpawnProcess-1 - 55164 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:30:47 - SpawnProcess-1 - 55164 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:30:47 - SpawnProcess-1 - 55164 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:30:48 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:30:48 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (128, 128)
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:30:52 - SpawnProcess-1 - 55164 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:32:45 - MainProcess - 17864 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:32:47 - SpawnProcess-1 - 55512 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:32:52 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:32:52 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (160, 160)
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:32:55 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:33:34 - SpawnProcess-1 - 55512 - common - WARNING - [screen_record.py:60] - ffmpeg 进程已提前退出，无法发送退出命令。
2025-07-29 17:33:41 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:33:41 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 976 x 1038
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (952, 0)
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:33:44 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:34:00 - SpawnProcess-1 - 55512 - common - WARNING - [screen_record.py:60] - ffmpeg 进程已提前退出，无法发送退出命令。
2025-07-29 17:34:09 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:34:09 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (960, 0)
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:34:12 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:34:13 - SpawnProcess-1 - 55512 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaMobile\zip\20250729\北京_mobile_831185_资费数据.mp4
2025-07-29 17:35:00 - MainProcess - 41252 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:35:01 - SpawnProcess-1 - 56796 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:35:31 - MainProcess - 18316 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:35:32 - MainProcess - 18316 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:35:32 - MainProcess - 18316 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:35:32 - MainProcess - 18316 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 960 x 540
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (166, 0)
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:35:55 - MainProcess - 18316 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-07-29 17:35:55 - MainProcess - 18316 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:35:55 - MainProcess - 18316 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:37:11 - MainProcess - 51928 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:37:13 - SpawnProcess-1 - 19748 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:37:19 - MainProcess - 17904 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:37:19 - MainProcess - 17904 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:37:19 - MainProcess - 17904 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:37:19 - MainProcess - 17904 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 960 x 540
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 714
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (221, 276)
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:37:25 - MainProcess - 17904 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 232 - 管道正在被关闭。
2025-07-29 17:37:25 - MainProcess - 17904 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:37:25 - MainProcess - 17904 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 232 - 管道正在被关闭。
2025-07-29 17:41:25 - MainProcess - 12304 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:41:27 - SpawnProcess-1 - 60848 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:41:34 - MainProcess - 44836 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:41:34 - MainProcess - 44836 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:41:34 - MainProcess - 44836 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:41:34 - MainProcess - 44836 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 960 x 540
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (117, 185)
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:41:40 - MainProcess - 44836 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 232 - 管道正在被关闭。
2025-07-29 17:41:40 - MainProcess - 44836 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:41:40 - MainProcess - 44836 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:44:42 - MainProcess - 24348 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:44:43 - SpawnProcess-1 - 12584 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:44:54 - MainProcess - 60664 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:44:55 - MainProcess - 60664 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:44:55 - MainProcess - 60664 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:44:55 - MainProcess - 60664 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 960 x 540
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (566, 62)
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:45:00 - MainProcess - 60664 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 232 - 管道正在被关闭。
2025-07-29 17:45:00 - MainProcess - 60664 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:45:00 - MainProcess - 60664 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 232 - 管道正在被关闭。
2025-07-29 17:48:18 - MainProcess - 8740 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:48:21 - SpawnProcess-1 - 34796 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:48:45 - MainProcess - 3104 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:49:06 - MainProcess - 8488 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:49:12 - MainProcess - 17760 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:49:19 - MainProcess - 2460 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:49:42 - MainProcess - 1452 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:50:06 - MainProcess - 31964 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:50:24 - MainProcess - 52684 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:50:26 - SpawnProcess-1 - 1252 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:50:30 - MainProcess - 59968 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:50:30 - MainProcess - 59968 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:50:30 - MainProcess - 59968 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:50:30 - MainProcess - 59968 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 960 x 540
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (584, 20)
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:50:36 - MainProcess - 59968 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:50:36 - MainProcess - 59968 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:50:36 - MainProcess - 59968 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:50:43 - MainProcess - 59968 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:50:43 - MainProcess - 59968 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:52:39 - MainProcess - 55616 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:52:41 - SpawnProcess-1 - 53676 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:52:43 - MainProcess - 39748 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:52:44 - MainProcess - 39748 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:52:44 - MainProcess - 39748 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:52:44 - MainProcess - 39748 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 768 x 432
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (149, 86)
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:52:50 - MainProcess - 39748 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:52:50 - MainProcess - 39748 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:52:50 - MainProcess - 39748 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:52:57 - MainProcess - 39748 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:52:57 - MainProcess - 39748 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:53:16 - MainProcess - 48152 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:53:18 - SpawnProcess-1 - 20208 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:53:24 - MainProcess - 15300 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:53:24 - MainProcess - 15300 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:53:24 - MainProcess - 15300 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:53:24 - MainProcess - 15300 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 768 x 432
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (418, 12)
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:53:30 - MainProcess - 15300 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:53:30 - MainProcess - 15300 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:53:30 - MainProcess - 15300 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:53:38 - MainProcess - 15300 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:53:38 - MainProcess - 15300 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:54:18 - MainProcess - 34256 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:54:20 - SpawnProcess-1 - 55268 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:54:23 - MainProcess - 51036 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:54:23 - MainProcess - 51036 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:54:23 - MainProcess - 51036 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:54:23 - MainProcess - 51036 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-29 17:54:29 - MainProcess - 51036 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:54:29 - MainProcess - 51036 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 768 x 432
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (960, 0)
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 960 x 1040
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:86] - 目标位置: (960, 0)
2025-07-29 17:54:30 - MainProcess - 51036 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:54:30 - MainProcess - 51036 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:54:30 - MainProcess - 51036 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:54:38 - MainProcess - 51036 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:54:38 - MainProcess - 51036 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:55:18 - MainProcess - 20896 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:55:19 - SpawnProcess-1 - 51268 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250729
2025-07-29 17:55:27 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:55:27 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 960 x 1040
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (9, 61)
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-29 17:55:30 - SpawnProcess-1 - 51268 - common - INFO - [screen_record.py:53] - 开始录屏...
