import os
import re
import random
import time
from datetime import datetime
from typing import Optional, Union, Any
from pathlib import Path
import logging

# 导入不同类型的logger
try:
    from common.logger_config import mobile_logger as logger
except ImportError:
    try:
        from common.logger_config import unicom_logger as logger
    except ImportError:
        try:
            from common.logger_config import telecom_logger as logger
        except ImportError:
            try:
                from common.logger_config import broadnet_logger as logger
            except ImportError:
                logger = logging.getLogger(__name__)

class HTMLSaver:
    """通用HTML保存工具类"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """初始化HTML保存器
        
        Args:
            base_dir: 基础目录路径，如果为None则使用当前文件所在目录
        """
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent.parent
        self.current_date = datetime.now().strftime('%Y%m%d')
        
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 替换Windows文件系统不允许的字符
        invalid_chars = r'[<>:"/\\|?*]'
        # 使用下划线替换非法字符
        sanitized = re.sub(invalid_chars, '_', filename)
        # 移除首尾的空白字符
        sanitized = sanitized.strip()
        # 如果文件名为空，使用默认名称
        if not sanitized:
            sanitized = "unknown"
        return sanitized
    
    def _ensure_province_dir(self, province_code: str, operator: str) -> str:
        """确保省份数据目录存在
        
        Args:
            province_code: 省份编码
            operator: 运营商名称 (mobile/unicom/telecom/broadnet)
            
        Returns:
            str: 省份数据目录路径
        """
        # 构建目录路径：base_dir/ChinaOperator/data/current_date/province_code
        operator_map = {
            "mobile": "ChinaMobile",
            "unicom": "ChinaUnicom", 
            "telecom": "ChinaTelecom",
            "broadnet": "ChinaBroadnet"
        }
        
        operator_dir = operator_map.get(operator, operator)
        province_dir = self.base_dir / operator_dir / "data" / self.current_date / province_code.lower()
        province_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"确保省份数据目录存在: {province_dir}")
        return str(province_dir)
    
    def _generate_filename(self, province_name: str, page_info: str = "", suffix: str = "html") -> str:
        """生成文件名
        
        Args:
            province_name: 省份名称
            page_info: 页面信息（如页码、菜单名称等）
            suffix: 文件扩展名
            
        Returns:
            str: 生成的文件名
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_num = random.randint(1000, 9999)
        
        # 清理文件名
        province_name_sanitized = self._sanitize_filename(province_name)
        page_info_sanitized = self._sanitize_filename(page_info) if page_info else ""
        
        # 构建文件名
        if page_info_sanitized:
            filename = f"{province_name_sanitized}_{timestamp}_{random_num}_{page_info_sanitized}.{suffix}"
        else:
            filename = f"{province_name_sanitized}_{timestamp}_{random_num}.{suffix}"
        
        return filename
    
    async def save_playwright_html(self, 
                                  page: Any, 
                                  province_code: str, 
                                  province_name: str, 
                                  operator: str,
                                  page_info: str = "",
                                  wait_for_load: bool = True,
                                  wait_timeout: int = 10000) -> Optional[str]:
        """保存Playwright页面的HTML内容
        
        Args:
            page: Playwright页面对象
            province_code: 省份编码
            province_name: 省份名称
            operator: 运营商名称
            page_info: 页面信息（如页码、菜单名称等）
            wait_for_load: 是否等待页面加载完成
            wait_timeout: 等待超时时间（毫秒）
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败则返回None
        """
        try:
            # 等待页面完全加载
            if wait_for_load:
                await page.wait_for_load_state('networkidle', timeout=wait_timeout)
                await page.wait_for_timeout(2000)  # 额外等待2秒确保内容完全加载
            
            # 获取页面HTML内容
            html_content = await page.content()
            
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(province_code, operator)
            
            # 生成文件名
            filename = self._generate_filename(province_name, page_info, "html")
            filepath = os.path.join(province_dir, filename)
            
            # 保存HTML内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Playwright HTML已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存Playwright HTML时发生错误: {str(e)}")
            return None
    
    def save_selenium_html(self, 
                          driver: Any, 
                          province_code: str, 
                          province_name: str, 
                          operator: str,
                          page_info: str = "",
                          wait_for_load: bool = True,
                          wait_timeout: int = 10) -> Optional[str]:
        """保存Selenium页面的HTML内容
        
        Args:
            driver: Selenium WebDriver对象
            province_code: 省份编码
            province_name: 省份名称
            operator: 运营商名称
            page_info: 页面信息（如页码、菜单名称等）
            wait_for_load: 是否等待页面加载完成
            wait_timeout: 等待超时时间（秒）
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败则返回None
        """
        try:
            # 等待页面完全加载
            if wait_for_load:
                # 等待页面加载完成
                driver.implicitly_wait(wait_timeout)
                time.sleep(2)  # 额外等待2秒确保内容完全加载
            
            # 获取页面HTML内容
            html_content = driver.page_source
            
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(province_code, operator)
            
            # 生成文件名
            filename = self._generate_filename(province_name, page_info, "html")
            filepath = os.path.join(province_dir, filename)
            
            # 保存HTML内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Selenium HTML已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存Selenium HTML时发生错误: {str(e)}")
            return None
    
    def save_api_response(self, 
                         response_body: str, 
                         province_code: str, 
                         province_name: str, 
                         operator: str,
                         api_info: str = "") -> Optional[str]:
        """保存API响应内容
        
        Args:
            response_body: API响应内容
            province_code: 省份编码
            province_name: 省份名称
            operator: 运营商名称
            api_info: API信息（如接口名称等）
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败则返回None
        """
        try:
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(province_code, operator)
            
            # 生成文件名
            filename = self._generate_filename(province_name, api_info, "json")
            filepath = os.path.join(province_dir, filename)
            
            # 保存API响应内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(response_body)
            
            logger.info(f"API响应已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存API响应时发生错误: {str(e)}")
            return None
    
    def save_screenshot(self, 
                       driver_or_page: Any, 
                       province_code: str, 
                       province_name: str, 
                       operator: str,
                       page_info: str = "",
                       is_playwright: bool = False) -> Optional[str]:
        """保存页面截图
        
        Args:
            driver_or_page: Selenium WebDriver或Playwright页面对象
            province_code: 省份编码
            province_name: 省份名称
            operator: 运营商名称
            page_info: 页面信息（如页码、菜单名称等）
            is_playwright: 是否为Playwright对象
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败则返回None
        """
        try:
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(province_code, operator)
            
            # 生成文件名
            filename = self._generate_filename(province_name, page_info, "png")
            filepath = os.path.join(province_dir, filename)
            
            # 保存截图
            if is_playwright:
                # Playwright截图
                driver_or_page.screenshot(path=filepath)
            else:
                # Selenium截图
                driver_or_page.save_screenshot(filepath)
            
            logger.info(f"页面截图已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存页面截图时发生错误: {str(e)}")
            return None

# 创建全局实例
html_saver = HTMLSaver() 