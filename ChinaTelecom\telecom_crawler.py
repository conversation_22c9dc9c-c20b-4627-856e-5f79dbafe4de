import os
import sys
import json
import random
from datetime import datetime
from typing import Dict, Any
import zipfile
import asyncio
import aiohttp
import re
import argparse


# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from playwright.async_api import async_playwright, Page
from common.logger_config import telecom_logger as logger
from province_parsers import (
    heilongjiang, ningxia, hebei, anhui, guangdong,
    jiangxi, xinjiang, yunnan, jiangsu, shandong, gansu, hainan, hunan, xizang, zhejiang
)
# 导入ScreenRecorder
from common.screen_record import ScreenRecorder
# 导入窗口操作工具
from common.window_utils import split_window_to_right_half, move_window_to_bottom_right, get_adaptive_window_size, activate_window

# 导入公共回调方法
from common.callback_utils import async_callback
# 导入HTML保存工具
from common.html_saver import html_saver
# 导入配置加载工具
from common.config_loader import is_screen_recording_enabled

# 省份解析器映射
PROVINCE_PARSERS = {
    'hl': (heilongjiang.parse_heilongjiang_content, heilongjiang.get_heilongjiang_menu_config),
    'nx': (ningxia.parse_ningxia_content, ningxia.get_ningxia_menu_config),
    'he': (hebei.parse_hebei_content, hebei.get_hebei_menu_config),
    'ah': (anhui.parse_anhui_content, anhui.get_anhui_menu_config),
    'gd': (guangdong.parse_guangdong_content, guangdong.get_guangdong_menu_config),
    'jx': (jiangxi.parse_jiangxi_content, jiangxi.get_jiangxi_menu_config),
    'xj': (xinjiang.parse_xinjiang_content, xinjiang.get_xinjiang_menu_config),
    'yn': (yunnan.parse_yunnan_content, yunnan.get_yunnan_menu_config),
    'js': (jiangsu.parse_jiangsu_content, jiangsu.get_jiangsu_menu_config),
    'sd': (shandong.parse_shandong_content, shandong.get_shandong_menu_config),
    'gs': (gansu.parse_gansu_content, gansu.get_gansu_menu_config),
    'hi': (hainan.parse_hainan_content, hainan.get_hainan_menu_config),
    'hn': (hunan.parse_hunan_content, hunan.get_hunan_menu_config),
    'xz': (xizang.parse_xizang_content, xizang.get_xizang_menu_config),
    'zj': (zhejiang.parse_zhejiang_content, zhejiang.get_zhejiang_menu_config)
}

class TelecomSpider:
    """中国电信资费查询爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
        self.province_name = None
        self.province_code = None
        self.item_text=None
        # 硬编码配置
        self.PAGE_LOAD_TIMEOUT = 30000  # 设置为30秒
        self.ELEMENT_WAIT_TIMEOUT = 30000  # 设置为30秒
        self.MAX_RETRIES = 3  # 最大重试次数
        self.BASE_URL = "https://www.189.cn/jtzfzq/bj/"
        
        # 省份名称映射
        self.PROVINCE_NAMES = {
            'bj': '北京',
            'ah': '安徽',
            'cq': '重庆',
            'fj': '福建',
            'gd': '广东',
            'gs': '甘肃',
            'gx': '广西',
            'gz': '贵州',
            'hb': '湖北',
            'hn': '湖南',
            'he': '河北',
            'ha': '河南',
            'hi': '海南',
            'hl': '黑龙江',
            'js': '江苏',
            'jl': '吉林',
            'jx': '江西',
            'ln': '辽宁',
            'nm': '内蒙古',
            'nx': '宁夏',
            'qh': '青海',
            'sd': '山东',
            'sh': '上海',
            'sx': '山西',
            'sn': '陕西',
            'sc': '四川',
            'tj': '天津',
            'xj': '新疆',
            'xz': '西藏',
            'yn': '云南',
            'zj': '浙江'
        }
        
        # API URL配置
        self.API_URLS = {
            'bj': 'https://bj.189.cn/expensesZoneDetail/queryExpensesZoneList.action',
            'cq': 'https://cq.189.cn/scloud/ecscactivity/psotage/queryNewPostAge',
            'fj': 'https://fj.189.cn/ic/api/shop/menu/getTariffListData',
            # 'gs': 'https://gs.189.cn/api/ecdpapi/prod/tariff-zone/list',
            'gx': 'https://gx.189.cn/shop/ord/qryZf',
            'gz': 'https://m.gz.189.cn/wdweb/zfzq/cp/queryZfzqList', 
            'hb': 'https://hb.189.cn/tariff/hb/index',
            'ha': 'https://wxapp.ha.189.cn/nethall/netHallApi/packageTariff/infos',
            'jl': 'https://jl.189.cn/service/bill/toFeeArea.action',
            'ln': 'https://wapln.189.cn/webapi/tariff-area!queryTariffInfoWt.action',
            'nm': 'https://saopnm.189.cn/udw/pub/expensesnew/search',
            'qh': 'https://wapqh.189.cn/zfzq/getData', 
            'sd': 'https://wapsd.189.cn/self/tariffSE/getTariffListNew',
            'sh': 'https://sh.189.cn/newmall/static/tariffReport/pc/index.html',
            'sx': 'https://wx.sx.189.cn/sx_postage_show/tariffReport/queryAll',
            'sn': 'https://sn.189.cn/elec/multi/tariff/tariffAnnouncement',
            'sc': 'https://m.sc.189.cn/handlecloud/tariffZone/qryOfferReportDetail', 
            'tj': 'https://tj.189.cn/tj/postageNew/queryPostage.action',
            'yn': 'https://yn.189.cn/tczf/tc_detail.jsp'
            # 'zj': 'https://wapzj.189.cn/svchub/bus/content/qryZFZQDetail',     
            
            # 可以添加更多省份的API URL
        }
        
        # 获取当前文件所在目录的绝对路径
        self.DATA_DIR = os.path.join(current_dir, "data")
        self.ZIP_DIR = os.path.join(current_dir, "zip")
        
        # 确保目录存在
        for dir_path in [self.DATA_DIR, self.ZIP_DIR]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"创建目录: {dir_path}")
        
        # 存储所有产品数据
        self.all_products_data = {
            "local": {},  # 本地资费（可能是北京、上海等）
            "集团资费公示": {}
        }
        
        # 存储生成的JSON文件路径
        self.json_files = []
        
        # 存储第一个选项卡的文本
        self.first_tab_text = None

        # 省份菜单配置（获取接口返回数据）
        self.PROVINCE_MENU_CONFIG = {
            # 'zj': {
            #     'wait_selector': "div.model.category-model.fix-tab",
            #     'menu_selector': "div.demo-tabs > div",
            #     'start_page': 1
            # },
            'sh': {
                'wait_selector': "div.index_classification",
                'menu_selector': "ul.nav_classification > li",
                'start_page': 1
            },
            'ln': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'jl': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'hb': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'bj': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div[style*='display: inline-block']",
                'start_page': 1
            },
            'cq': {
                'wait_selector': "div.ant-tabs-nav-list",
                'menu_selector': "div.ant-tabs-tab",
                'start_page': 1
            },
            'fj': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'gx': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'gz': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1
            },
            'ha': {
                'wait_selector': "div.NewTariffZone_box_top",
                'menu_selector': "div.top_box_li",
                'start_page': 1
            },
            'nm': {
                'wait_selector': "div.index_classification",
                'menu_selector': "li.classification_list",
                'start_page': 1
            },
            'qh': {
                'wait_selector': "div.category",
                'menu_selector': "div.category > div > h1",
                'start_page': 1
            },
            'sx': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 0,
                'end_page': 0
            },
            'sn': {
                'wait_selector': "div.content > div",
                'menu_selector': "div.fenlei > p",
                'start_page': 1
            },
            'sc': {
                'wait_selector': "div.menu-box",
                'menu_selector': "#tabTwoBox > div",
                'start_page': 1
            },
            'tj': {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 0,
                'end_page': 0
            }
        }

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 替换Windows文件系统不允许的字符
        invalid_chars = r'[<>:"/\\|?*]'
        # 使用下划线替换非法字符
        sanitized = re.sub(invalid_chars, '_', filename)
        # 移除首尾的空白字符
        sanitized = sanitized.strip()
        # 如果文件名为空，使用默认名称
        if not sanitized:
            sanitized = "unknown"
        return sanitized

    async def setup_driver(self) -> None:
        """初始化Playwright浏览器"""
        logger.info("正在初始化Playwright...")
        try:
            self.playwright = await async_playwright().start()
            
            # 添加随机User-Agent
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
            ]
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=[
                    '--start-maximized',  # 启动时最大化
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                    '--disable-site-isolation-trials',
                    '--disable-blink-features=AutomationControlled'
                ]
            )
            
            # 创建上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},  # 设置较大的视口大小
                user_agent=random.choice(user_agents),
                ignore_https_errors=True
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(self.PAGE_LOAD_TIMEOUT)
            
            # 直接访问资费专区页面
            logger.info("正在访问资费专区页面...")
            for retry in range(self.MAX_RETRIES):
                try:
                    # 先等待页面加载完成
                    response = await self.page.goto(self.BASE_URL, wait_until='domcontentloaded')
                    
                    if not response:
                        raise Exception("页面响应为空")
                    
                    if not response.ok:
                        logger.warning(f"访问资费专区页面失败，状态码: {response.status}，重试 {retry + 1}/{self.MAX_RETRIES}")
                        if retry == self.MAX_RETRIES - 1:
                            raise Exception(f"访问资费专区页面失败，状态码: {response.status}")
                        await self._random_sleep(2, 3)
                        continue
                    
                    # 检查页面是否真正加载完成
                    try:
                        # 等待页面主要内容加载
                        await self.page.wait_for_selector('body', state='visible', timeout=5000)
                        logger.info("页面主要内容已加载")
                        break
                    except Exception as e:
                        logger.warning(f"等待页面内容加载超时: {str(e)}")
                        if retry == self.MAX_RETRIES - 1:
                            raise Exception("页面内容加载失败")
                        await self._random_sleep(2, 3)
                        continue
                        
                except Exception as e:
                    if retry == self.MAX_RETRIES - 1:
                        logger.error(f"访问资费专区页面最终失败: {str(e)}")
                        raise
                    logger.warning(f"访问资费专区页面出错: {str(e)}，重试 {retry + 1}/{self.MAX_RETRIES}")
                    await self._random_sleep(2, 3)
            
        except Exception as e:
            logger.error(f"初始化驱动时发生错误: {str(e)}", exc_info=True)
            await self.cleanup()
            raise

    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                await self.browser.close()
                logger.info("浏览器已关闭")
            if self.playwright:
                await self.playwright.stop()
                logger.info("Playwright已停止")
        except Exception as e:
            logger.error(f"清理资源时发生错误: {str(e)}")

    async def _random_sleep(self, min_seconds=1, max_seconds=3):
        """随机等待一段时间"""
        sleep_time = random.uniform(min_seconds, max_seconds)
        logger.debug(f"随机等待 {sleep_time:.2f} 秒")
        await asyncio.sleep(sleep_time)

    async def handle_menu_items(self) -> None:
        """处理菜单项"""
        try:
            logger.info("确保焦点在当前页面...")
            
            # 获取当前页面URL和标题
            current_url = self.page.url
            current_title = await self.page.title()
            logger.info(f"当前页面URL: {current_url}")
            logger.info(f"当前页面标题: {current_title}")
            
            # 1. 处理顶部的城市/集团资费选项
            logger.info("处理顶部的城市/集团资费选项...")
            top_tabs = await self.page.query_selector_all("div.top-tab")
            
            if not top_tabs:
                raise Exception("未找到顶部的城市/集团资费选项")
            
            logger.info(f"找到 {len(top_tabs)} 个顶部选项")
            
            # 保存第一个选项卡的文本
            first_tab = await top_tabs[0].query_selector("p")
            if first_tab:
                self.first_tab_text = await first_tab.text_content()
                logger.info(f"第一个选项卡文本: {self.first_tab_text}")
            # 遍历处理顶部tab（从1开始，或根据实际需求）
            for idx in range(len(top_tabs)):
                try:
                    # 1. 刷新页面
                    await self.page.reload()
                    await self._random_sleep(2, 3)
                    # 2. 重新查找 top_tabs
                    top_tabs = await self.page.query_selector_all("div.top-tab")
                    if idx >= len(top_tabs):
                        logger.warning(f"tab索引{idx}超出范围")
                        continue
                    tab = top_tabs[idx]
                    # 3. 获取tab文本
                    tab_text_elem = await tab.query_selector("p")
                    tab_text = await tab_text_elem.text_content() if tab_text_elem else ""
                    logger.info(f"处理顶部选项: {tab_text}")
                    await tab.wait_for_element_state('visible')
                    await tab.click()
                    logger.info(f"成功点击顶部选项: {tab_text}")
                    await self._random_sleep(3, 5)
                    
                    # 保存当前页面HTML
                    await html_saver.save_playwright_html(
                        page=self.page,
                        province_code=self.province_code,
                        province_name=self.PROVINCE_NAMES.get(self.province_code, self.province_code),
                        operator="telecom",
                        page_info=f"顶部选项_{tab_text}"
                    )
                    
                    if self.first_tab_text and tab_text == self.first_tab_text:
                        if self.province_code in self.PROVINCE_MENU_CONFIG.keys():  
                            await self._handle_network_request_content()
                        else:
                            await self._handle_html_content()

                            if self.province_code!='js':
                                await self._save_current_data(tab_text)
                        
                    else:   
                        await self._handle_menu_content()
                        await self._save_current_data(tab_text)
                    await self._random_sleep(1, 2)
                except Exception as e:
                    logger.error(f"处理顶部选项时发生错误: {str(e)}")
                    continue
            logger.info("所有选项处理完成")
            
        except Exception as e:
            logger.error(f"处理菜单项时发生错误: {str(e)}", exc_info=True)
            raise

    async def _save_current_data(self, tab_text: str) -> None:
        """保存当前选项的数据
        
        Args:
            tab_text: 当前选项的文本
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d')
            random_num = random.randint(1000, 9999)
            if not os.path.exists(self.DATA_DIR):
                os.makedirs(self.DATA_DIR)
                logger.info(f"创建数据目录: {self.DATA_DIR}")
            self.province_name = self._sanitize_filename(self.PROVINCE_NAMES[self.province_code])
            # 新增：江苏省时加上城市名
            city_part = getattr(self, 'current_city', None)
            city_part = f"_{city_part}" if city_part else ""
            if tab_text == self.first_tab_text:
                data = {}
                for menu_type, products in self.all_products_data["local"].items():
                    data[f"{self.province_name}{city_part}_{menu_type}"] = products
                filename = f"{self.province_name}{city_part}_{timestamp}_{random_num}_{self.province_name}资费.json"
                province_dir = os.path.join(self.DATA_DIR, self.province_code)
                if not os.path.exists(province_dir):
                    os.makedirs(province_dir)
                    logger.info(f"创建省份目录: {province_dir}")
                filepath = os.path.join(province_dir, filename)
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    self.json_files.append(filepath)
                    logger.info(f"本地资费数据已保存到: {filename}")
                except Exception as e:
                    logger.error(f"保存文件 {filename} 时发生错误: {str(e)}")
                    raise
            else:
                data = {}
                for menu_type, products in self.all_products_data["集团资费公示"].items():
                    data[f"集团资费{city_part}_{menu_type}"] = products
                filename = f"{self.province_name}{city_part}_{timestamp}_{random_num}_集团资费公示.json"
                province_dir = os.path.join(self.DATA_DIR, self.province_code)
                if not os.path.exists(province_dir):
                    os.makedirs(province_dir)
                    logger.info(f"创建省份目录: {province_dir}")
                filepath = os.path.join(province_dir, filename)
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    self.json_files.append(filepath)
                    logger.info(f"集团资费数据已保存到: {filename}")
                except Exception as e:
                    logger.error(f"保存文件 {filename} 时发生错误: {str(e)}")
                    raise
        except Exception as e:
            logger.error(f"保存数据时发生错误: {str(e)}")
            raise

    async def _wait_for_page_load(self, page: Page, selector: str, timeout: int = 10000) -> None:
        """等待页面加载完成
        
        Args:
            page: Playwright页面对象
            selector: 要等待的元素选择器
            timeout: 超时时间（毫秒）
        """
        # 等待2-3秒让页面完全加载
        logger.info("等待2-3秒让页面完全加载...")
        await self._random_sleep(2, 3)
        
        # 等待指定元素加载
        await page.wait_for_selector(selector, timeout=timeout)

    async def _parse_product_data(self, product) -> dict:
        """解析产品数据
        
        Args:
            product: 产品元素
            
        Returns:
            dict: 产品数据
        """
        try:
            # 获取产品标题和编号
            title_elem = await product.query_selector("div.top.clearfix > p.title")
            code_elem = await product.query_selector("div.top.clearfix > p.small")
            
            title = await title_elem.text_content() if title_elem else "未知标题"
            code = await code_elem.text_content() if code_elem else "未知编号"
            
            # 提取方案编号，删除空格
            if "方案编号：" in code:
                code = code.split("方案编号：")[1].strip()
            code = code.replace(" ", "")
            
            # 获取基本信息
            basic_info = {}
            basic_info_elems = await product.query_selector_all("div.text.jbxx div.clearfix div p")
            for elem in basic_info_elems:
                text = await elem.text_content()
                if "：" in text:
                    key, value = text.split("：", 1)
                    basic_info[key.strip()] = value.strip()
            
            # 获取服务内容
            service_content = {}
            table = await product.query_selector("div.table-out.ffnr table")
            if table:
                rows = await table.query_selector_all("tr")
                if len(rows) >= 2:
                    # 获取表头（第一行）
                    header_cells = await rows[0].query_selector_all("th")
                    header_names = [await cell.text_content() for cell in header_cells if await cell.text_content()]

                    # 获取数值（第二行）
                    value_cells = await rows[1].query_selector_all("td")
                    value_texts = [await cell.text_content() for cell in value_cells if await cell.text_content()]

                    # 将表头和数值配对
                    min_len = min(len(header_names), len(value_texts))
                    for i in range(min_len):
                        service_content[header_names[i].strip()] = value_texts[i].strip()
            
            # 获取备注信息
            remark_elem = await product.query_selector("div.remark.other_content")
            remark = ""
            others = ""
            if remark_elem:
                # 获取所有备注段落
                remark_paragraphs = await remark_elem.query_selector_all("p")
                remark_texts = []
                for p in remark_paragraphs:
                    text = await p.text_content()
                    if text.strip():
                        if text.startswith("其他说明："):
                            others = text.replace("其他说明：", "").strip()
                        else:
                            remark_texts.append(text.strip())
                remark = "\n".join(remark_texts)
            
            # 构建产品数据
            return {
                "title": title,
                "code": code,
                "basic_info": basic_info,
                "service_content": service_content,
                "remark": remark,
                "others": others
            }
            
        except Exception as e:
            logger.error(f"解析产品数据时发生错误: {str(e)}")
            return None

    async def _handle_menu_content(self) -> None:
        """处理菜单内容"""
        try:

            # 等待页面加载
            await self._random_sleep(3, 5)
            
            # 等待菜单区域出现
            logger.info("等待菜单区域出现...")
            await self.page.wait_for_selector("div.top.menu_tab.clearfix", timeout=10000)
            
            # 获取所有菜单标签项
            logger.info("获取菜单标签项...")
            menu_tabs = await self.page.query_selector_all("div.menu-tab-item")
            
            if not menu_tabs:
                raise Exception("未找到菜单标签项")
            
            logger.info(f"找到 {len(menu_tabs)} 个菜单标签项")
            
            # 遍历处理每个菜单标签项
            for tab in menu_tabs:
                try:
                    # 获取标签文本
                    tab_text = await tab.text_content()
                    logger.info(f"处理菜单标签: {tab_text}")
                    
                    # 跳过国际及港澳台标准资费
                    if tab_text == "国际及港澳台标准资费":
                        logger.info(f"跳过处理: {tab_text}")
                        continue
                    
                    # 确保元素可见和可点击
                    await tab.wait_for_element_state('visible')
                    
                    # 点击标签
                    await tab.click()
                    logger.info(f"成功点击菜单标签: {tab_text}")
                    
                    # 等待页面加载
                    await self._wait_for_page_load(self.page, "div.list-box")
                    
                    # 解析页面内容
                    await self._parse_group_content(tab_text)
                    
                    # 随机等待一段时间
                    await self._random_sleep(1, 2)
                    
                    # 在数据加载完成后保存HTML结构
                    await self._save_html_structure(self.page, tab_text)
                except Exception as e:
                    logger.error(f"处理菜单标签 {tab_text} 时发生错误: {str(e)}")
                    continue
            
            logger.info("菜单内容处理完成")
            
        except Exception as e:
            logger.error(f"处理菜单内容时发生错误: {str(e)}")
            raise

    async def _parse_group_content(self, menu_type: str) -> None:
        """解析集团资费页面内容
        
        Args:
            menu_type: 菜单类型
        """
        try:
            logger.info(f"开始解析{menu_type}的页面内容...")
            
            # 等待列表区域加载
            await self.page.wait_for_selector("div.list-box", timeout=10000)
            
            # 获取所有产品项
            products = await self.page.query_selector_all("div.list-box > div")
            
            if not products:
                logger.warning(f"未找到{menu_type}的产品项")
                return
            
            total_products = len(products)
            logger.info(f"找到 {total_products} 个产品项")
            
            # 存储当前类型的产品数据
            products_data = []
            
            logger.info(f"开始解析{menu_type}产品，共{total_products}个")
            
            # 遍历处理每个产品
            for index, product in enumerate(products, 1):
                try:
                    # 获取产品标题用于日志
                    title_elem = await product.query_selector("div.top.clearfix > p.title")
                    title = await title_elem.text_content() if title_elem else "未知标题"
                    
                    # 每10个产品输出一次进度
                    if index % 10 == 0 or index == total_products:
                        logger.info(f"已处理 {index}/{total_products} 个产品")
                    
                    # 解析产品数据
                    product_data = await self._parse_product_data(product)
                    if product_data:
                        products_data.append(product_data)
                        # 只在成功时记录产品标题
                        logger.info(f"成功解析: {product_data['title']}")
                    else:
                        logger.warning(f"产品解析失败: {title}")
                    
                except Exception as e:
                    logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                    continue
            
            logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
            
            if products_data:
                # 将数据存储到总数据中
                self.all_products_data["集团资费公示"][menu_type] = products_data
                logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
            else:
                logger.warning(f"{menu_type}没有解析到任何产品数据")
            
        except Exception as e:
            logger.error(f"解析页面内容时发生错误: {str(e)}")
            raise

    async def click_all_products(self) -> None:
        """点击每个产品类型中的'全部'按钮"""
        try:
            logger.info("等待产品类型区域加载...")
            bot_section = await self.page.wait_for_selector("div.bot")
            logger.debug("找到产品类型区域")
            
            product_sections = await bot_section.query_selector_all("div.clearfix")
            logger.info(f"找到 {len(product_sections)} 个产品类型区域")
            
            for section in product_sections:
                try:
                    section_name = await section.query_selector("p.label")
                    section_name_text = await section_name.text_content()
                    logger.info(f"处理产品类型区域: {section_name_text}")
                    
                    all_button = await section.query_selector("li[data-type='全部']")
                    if all_button:
                        logger.debug(f"找到{section_name_text}的'全部'按钮")
                        await all_button.click()
                        logger.info(f"成功点击{section_name_text}的'全部'按钮")
                        await self._random_sleep(1, 2)
                    else:
                        logger.warning(f"{section_name_text}区域未找到'全部'按钮")
                        
                except Exception as e:
                    logger.error(f"处理{section_name_text}区域时发生错误: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"点击'全部'按钮时发生错误: {str(e)}", exc_info=True)
            raise

    async def save_and_compress_data(self) -> tuple:
        """压缩已保存的JSON文件
        
        Returns:
            tuple: (zip文件路径, 状态码)
        """
        try:
            # 检查是否有数据需要压缩
            if not self.json_files:
                logger.error("没有数据需要压缩")
                return None, "500"
            
            # 确保zip目录存在
            if not os.path.exists(self.ZIP_DIR):
                os.makedirs(self.ZIP_DIR)
                logger.info(f"创建压缩目录: {self.ZIP_DIR}")
            
            # 生成时间戳和随机数
            random_num = random.randint(1000, 9999)
            
            # 如果province_name未设置，使用省份代码获取名称
            if not self.province_name and self.province_code:
                self.province_name = self.PROVINCE_NAMES.get(self.province_code, self.province_code)
                logger.info(f"使用省份代码获取省份名称: {self.province_name}")
            
            # 创建ZIP文件
            zip_filename = f"{self.province_code}_telecom_{random_num}_资费数据.zip"
            zip_filepath = os.path.join(self.ZIP_DIR, zip_filename)
            
            with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for json_file in self.json_files:
                    if os.path.exists(json_file):
                        zipf.write(json_file, os.path.basename(json_file))
                    else:
                        logger.error(f"文件不存在: {json_file}")
                        return None, "500"
            
            # 检查zip文件是否成功创建且不为空
            if not os.path.exists(zip_filepath) or os.path.getsize(zip_filepath) == 0:
                logger.error("ZIP文件创建失败或为空")
                return None, "500"
            
            logger.info(f"数据已压缩到: {zip_filename}")
            return zip_filepath, "200"
            
        except Exception as e:
            logger.error(f"压缩数据时发生错误: {str(e)}")
            return None, "500"

    async def _send_callback(self, zip_filepath: str, callback_url: str, status: str = "200") -> None:
        """发送回调请求
        
        Args:
            zip_filepath: zip文件路径，可能为None
            callback_url: 回调URL
            status: 状态码，默认为"200"
            
        Raises:
            Exception: 回调失败时抛出异常
        """
        try:
            result = await async_callback(callback_url, zip_filepath, self.BASE_URL, status)
            logger.info(f"回调结果: {result}")
        except Exception as e:
            logger.error(f"发送回调时发生错误: {str(e)}")
            raise

    # 通过爬取页面获取数据
    async def _handle_html_content(self) -> None:
        """处理资费内容（iframe）"""
        try:
            logger.info("等待iframe加载...")
            iframe = await self.page.query_selector("iframe")
            if not iframe:
                raise Exception("未找到iframe")
            iframe_src = await iframe.get_attribute('src')
            logger.info(f"找到iframe，src: {iframe_src}")
            new_page = await self.context.new_page()
            iframe_urls = {
                'nx': 'https://yx.nx.189.cn/m_nx_main/newTariffZone/toIndex.do?source=H5',
                'he': 'https://waphe.189.cn/wap_heb/h5/productAnnounce/index.html',
                'ah': 'https://wapah.189.cn/tariff/h5toIndex.wap',
                'jx': 'https://wapjx.189.cn/wx/charges/index.html',
                'js': 'https://wapjs.189.cn/mall/pages/feePart/index.html',
                'gs': 'https://gs.189.cn/wt/tariffZone'
            }
            target_url = iframe_urls.get(self.province_code, iframe_src)
            await new_page.goto(target_url)

            anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
            # 激活并置顶Anaconda Prompt窗口
            activate_window(anaconda_window_titles, show_debug=True)

            # 江苏省特殊处理：iframe页面内遍历城市
            if self.province_code == 'js':
                await self.handle_all_cities_js(new_page)
                await new_page.close()
                logger.info("已关闭iframe页面")
                return
            # 其他省份原有逻辑
            parser_info = PROVINCE_PARSERS.get(self.province_code)
            if not parser_info:
                raise Exception(f"未找到省份 {self.province_code} 的解析器")
            parse_content, get_menu_config = parser_info
            menu_config = get_menu_config()
            logger.info("等待菜单区域出现...")
            await new_page.wait_for_selector(menu_config['wait_selector'], timeout=10000)
            menu_items = await new_page.query_selector_all(menu_config['menu_selector'])
            if not menu_items:
                raise Exception("未找到可见的菜单项")
            logger.info(f"找到 {len(menu_items)} 个可见菜单项")
            for item in menu_items:
                try:
                    item_text = await item.text_content()
                    logger.info(f"处理菜单项: {item_text}")
                    await item.wait_for_element_state('visible')
                    await item.click()
                    logger.info(f"成功点击菜单项: {item_text}")
                    await self._wait_for_page_load(new_page, menu_config['list_selector'])
                    await parse_content(new_page, item_text, self.all_products_data)
                    await self._random_sleep(1, 2)

                    # 在数据加载完成后保存HTML结构
                    await self._save_html_structure(new_page, item_text)
                    await self._random_sleep(12,15)
                except Exception as e:
                    logger.error(f"处理菜单项 {item_text} 时发生错误: {str(e)}")
                    continue
                
            await new_page.close()
            logger.info("已关闭iframe页面")
        except Exception as e:
            logger.error(f"处理资费内容时发生错误: {str(e)}")
            raise

    async def handle_all_cities_js(self, new_page):
        """江苏省页面：在iframe页面内遍历所有城市并采集数据"""
        try:
            logger.info("江苏省城市切换采集模式启动...")
            parser_info = PROVINCE_PARSERS.get(self.province_code)
            if not parser_info:
                raise Exception(f"未找到省份 {self.province_code} 的解析器")
            parse_content, get_menu_config = parser_info
            menu_config = get_menu_config()
            # 获取所有城市名列表（文本）
            # 等待城市选择器出现
            await new_page.wait_for_selector('div.city', timeout=10000)
            
            # 使用JavaScript直接显示下拉菜单
            await new_page.evaluate("""
                const cityElement = document.querySelector('div.city');
                const moreElement = document.querySelector('div.more');
                if (cityElement && moreElement) {
                    // 直接设置显示
                    moreElement.style.display = 'block';
                    // 触发鼠标进入事件
                    cityElement.dispatchEvent(new MouseEvent('mouseenter', {
                        bubbles: true,
                        cancelable: true
                    }));
                }
            """)
            await self._random_sleep(2, 3)
            
            # 等待下拉菜单出现
            await new_page.wait_for_selector('div.more', state='visible', timeout=5000)
            city_spans_inner = await new_page.query_selector_all('div.more > span')
            city_names = [await span.text_content() for span in city_spans_inner]
            logger.info(f"发现 {len(city_names)} 个城市选项")
            for idx, city_name in enumerate(city_names):
                try:
                    city_name = city_name.strip()
                    logger.info(f"处理城市: {city_name}")
                    # 1. 刷新页面
                    await new_page.reload()
                    await self._random_sleep(2, 3)
                    # 2. 重新 hover 并查找城市
                    # 等待城市选择器出现
                    await new_page.wait_for_selector('div.city', timeout=10000)
                    
                    # 使用JavaScript直接显示下拉菜单
                    await new_page.evaluate("""
                        const cityElement = document.querySelector('div.city');
                        const moreElement = document.querySelector('div.more');
                        if (cityElement && moreElement) {
                            // 直接设置显示
                            moreElement.style.display = 'block';
                            // 触发鼠标进入事件
                            cityElement.dispatchEvent(new MouseEvent('mouseenter', {
                                bubbles: true,
                                cancelable: true
                            }));
                        }
                    """)
                    await self._random_sleep(2, 3)
                    
                    # 等待下拉菜单出现
                    await new_page.wait_for_selector('div.more', state='visible', timeout=5000)
                    city_spans_inner = await new_page.query_selector_all('div.more > span')
                    found = False
                    for span in city_spans_inner:
                        name = await span.text_content()
                        if name.strip() == city_name:
                            await span.click()
                            await self._random_sleep(1, 2)
                            found = True
                            break
                    if not found:
                        logger.warning(f"未找到城市: {city_name}")
                        continue
                    self.current_city = city_name
                    # 3. 采集所有菜单项（重新查找）
                    await new_page.wait_for_selector(menu_config['wait_selector'], timeout=10000)
                    menu_items = await new_page.query_selector_all(menu_config['menu_selector'])
                    if not menu_items:
                        logger.warning(f"城市{city_name}未找到菜单项")
                        continue
                    for item in menu_items:
                        try:
                            item_text = await item.text_content()
                            logger.info(f"城市{city_name}处理菜单项: {item_text}")
                            await item.wait_for_element_state('visible')
                            await item.click()
                            await self._wait_for_page_load(new_page, menu_config['list_selector'])
                            await parse_content(new_page, item_text, self.all_products_data)
                            await self._random_sleep(1, 2)
                            # 在数据加载完成后保存HTML结构
                            await self._save_html_structure(new_page, item_text)
                        except Exception as e:
                            logger.error(f"城市{city_name}处理菜单项 {item_text} 时发生错误: {str(e)}")
                            continue
                    await self._save_current_data(self.first_tab_text or city_name)
                except Exception as e:
                    logger.error(f"采集城市 {city_name} 时发生错误: {str(e)}")
                    continue
            logger.info("江苏省所有城市采集完成")
        except Exception as e:
            logger.error(f"江苏省城市切换采集流程发生错误: {str(e)}", exc_info=True)
            raise

    async def _handle_network_request_content(self) -> None:
        """处理网络请求内容"""
        try:
            # 等待iframe加载    
            logger.info("等待iframe加载...")
            
            # 查找iframe
            iframe = await self.page.query_selector("iframe")
            if not iframe:
                raise Exception("未找到iframe")
            
            # 获取iframe的src
            iframe_src = await iframe.get_attribute('src')
            logger.info(f"找到iframe，src: {iframe_src}")
            
            # 创建新页面
            new_page = await self.context.new_page()
            
            anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
            # 激活并置顶Anaconda Prompt窗口
            activate_window(anaconda_window_titles, show_debug=True)

            # 设置请求拦截
            logger.info("设置请求拦截器...")
            
            async def intercept_response(route, request):
                """拦截并处理响应"""
                try:
                    
                    url = request.url
                    logger.debug(f"拦截到请求: {url}")
                    
                    # 继续请求
                    response = await route.fetch()
                    
                    # 获取当前省份的API URL
                    target_url = self.API_URLS.get(self.province_code)
                    if target_url and url.startswith(target_url):
                        try:
                            if response is None:
                                logger.error(f"API响应为空: {url}")
                                return
                            
                            response_body = await response.text()
                            if not response_body:
                                logger.error(f"API响应体为空: {url}")
                                return
                            
                            logger.debug(f"API响应内容: {response_body[:200]}...")
                            
                            # 保存响应到文件
                            await self._save_api_response(response_body, url)
                            
                            
                        except Exception as e:
                            logger.error(f"处理API响应时发生错误: {str(e)}")
                            logger.debug(f"请求URL: {url}")
                            logger.debug(f"请求方法: {request.method}")
                            logger.debug(f"请求头: {request.headers}")
                    
                    # 继续请求
                    await route.fulfill(response=response)
                
                except Exception as e:
                    logger.error(f"处理请求时发生错误: {str(e)}")
                    await route.continue_()
            
            # 设置请求拦截器
            await new_page.route("**/*", intercept_response)
            
            # 访问iframe页面
            await new_page.goto(iframe_src)
            
            # 获取省份菜单配置
            menu_config = self.PROVINCE_MENU_CONFIG.get(self.province_code)
            if not menu_config:
                raise Exception(f"未找到省份 {self.province_code} 的菜单配置")
            
            # 处理菜单项
            await self._handle_menu_items(new_page)
            
            # 关闭新页面
            await new_page.close()
            logger.info("已关闭iframe页面")
            
        except Exception as e:
            logger.error(f"处理网络请求内容时发生错误: {str(e)}")
            raise

    async def _save_html_structure(self, page: Page, menu_name: str) -> None:
        """保存页面HTML结构到文件
        
        Args:
            page: Playwright页面对象
            menu_name: 二级菜单名称
        """
        try:
            # 等待页面完全加载
            await page.wait_for_load_state('networkidle', timeout=10000)
            await self._random_sleep(2, 3)
            
            # 获取页面HTML内容
            html_content = await page.content()
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d')
            random_num = random.randint(1000, 9999)
            
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(self.province_code)
            
            # 构建文件名：省份名称_yyyymmdd_随机数_二级菜单名称.html
            province_name = self._sanitize_filename(self.PROVINCE_NAMES.get(self.province_code, self.province_code))
            menu_name_sanitized = self._sanitize_filename(menu_name)
            filename = f"{province_name}_{timestamp}_{random_num}_{menu_name_sanitized}.html"
            filepath = os.path.join(province_dir, filename)
            
            # 保存HTML内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML结构已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存HTML结构时发生错误: {str(e)}")

    async def _save_api_response(self, response_body: str, url: str) -> None:
        """保存API响应到文件
        
        Args:
            response_body: API响应内容
            url: 请求URL
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            random_num = random.randint(1000, 9999)
            
            # 确保省份目录存在
            province_dir = self._ensure_province_dir(self.province_code)
            
            # 构建文件名
            filename = f"{self.first_tab_text}_{timestamp}_{random_num}_{self.item_text if self.item_text else '套餐'}.json"
            filepath = os.path.join(province_dir, filename)
            logger.info(f"{self.first_tab_text}响应内容: {response_body[:100]}")
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(response_body)
            logger.info(f"响应已保存到文件: {filepath}")
            self.json_files.append(filepath)
            
        except Exception as e:
            logger.error(f"保存API响应到文件时发生错误: {str(e)}")

    def _ensure_province_dir(self, province_code: str) -> str:
        """确保省份数据目录存在
        
        Args:
            province_code: 省份编码
            
        Returns:
            str: 省份数据目录路径
        """
        province_dir = os.path.join(self.DATA_DIR, province_code.lower())
        if not os.path.exists(province_dir):
            os.makedirs(province_dir)
            logger.info(f"创建省份数据目录: {province_dir}")
        return province_dir

    async def _handle_menu_items(self, new_page: Page) -> None:
        """处理菜单项
        
        Args:
            new_page: Playwright页面对象
        """
        try:
            # 获取当前省份的菜单配置
            menu_config = self.PROVINCE_MENU_CONFIG.get(self.province_code, {
                'wait_selector': "div.menu-box",
                'menu_selector': "div.top.clearfix > div",
                'start_page': 1,
                'end_page': 10
            })
            
            # 等待菜单区域出现
            logger.info(f"等待菜单区域出现: {menu_config['wait_selector']}")
            await new_page.wait_for_selector(menu_config['wait_selector'], timeout=30000)
            
            # 等待菜单项出现
            await new_page.wait_for_selector(menu_config['menu_selector'], timeout=30000)

            await self._random_sleep(8,10)

            # 获取菜单项
            menu_items = await new_page.query_selector_all(menu_config['menu_selector'])
            if not menu_items:
                raise Exception(f"未找到菜单项: {menu_config['menu_selector']}")
            
            logger.info(f"找到 {len(menu_items)} 个菜单项")
            
            # 遍历处理每个菜单项
            for i in range(menu_config['start_page'], menu_config['end_page'] if menu_config.get('end_page') else len(menu_items)):
                try:
                    # 等待页面稳定
                    await self._random_sleep(2, 3)
                    
                    # 重新获取菜单项
                    menu_items = await new_page.query_selector_all(menu_config['menu_selector'])
                    if not menu_items or i >= len(menu_items):
                        logger.error("无法获取菜单项")
                        continue
                    
                    item = menu_items[i]
                    
                    # 获取菜单项文本
                    try:
                        item_text = await item.text_content()
                        logger.info(f"处理菜单项: {item_text}")
                        self.item_text = item_text
                    except Exception as e:
                        logger.error(f"获取菜单项文本时发生错误: {str(e)}")
                        continue
                    
                    # 确保元素可见和可点击
                    try:
                        await item.wait_for_element_state('visible')
                        await item.click()
                        logger.info(f"成功点击菜单项: {item_text}")
                        
                        # 等待一段时间确保响应被处理
                        await self._random_sleep(5, 6)
                        
                    except Exception as e:
                        logger.error(f"等待元素可见或点击时发生错误: {str(e)}")
                        continue
                    
                    # 随机等待一段时间
                    await self._random_sleep(1, 2)
                    
                except Exception as e:
                    logger.error(f"处理菜单项时发生错误: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"处理菜单项时发生错误: {str(e)}")
            raise

async def main(provinces: str = None, callback_url: str = None) -> tuple:
    spider = None
    zip_filepath = None
    status = "200"
    screen_recorder = None
    mp4_path = None
    try:
        logger.info("开始运行中国电信资费查询程序...")
        if sys.platform == 'win32':
            loop = asyncio.ProactorEventLoop()
            asyncio.set_event_loop(loop)
        spider = TelecomSpider()
        if provinces:
            spider.province_code=provinces
            if provinces in 'js':
                spider.BASE_URL='https://www.189.cn/jtzfzq/js2/?intaid=js-sy-hxfw-04-'
            else:
                spider.BASE_URL = f"https://www.189.cn/jtzfzq/{provinces}/"
            logger.info(f"使用省份URL: {spider.BASE_URL}")
        # ========== 路径生成开始 ==========
        import random
        from datetime import datetime
        province_code = provinces or 'unknown'
        rand_str = str(random.randint(1000, 9999))
        zip_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "zip")
        zip_filename = f"{province_code}_telecom_{rand_str}_资费数据.zip"
        zip_filepath = os.path.join(zip_dir, zip_filename)
        mp4_path = os.path.splitext(zip_filepath)[0] + ".mp4"
        # ========== 路径生成结束 ==========
        # ========== 录屏集成开始 ==========
        screen_recorder = None
        try:
            # 检查是否启用录屏
            if is_screen_recording_enabled():
                screen_recorder = ScreenRecorder(output_file=mp4_path, capture_method='dshow')
                screen_recorder.start_recording()
                logger.info(f"录屏已开始，文件: {mp4_path}")
            else:
                logger.info("录屏功能已禁用")
        except Exception as e:
            logger.error(f"录屏启动失败: {str(e)}")
            screen_recorder = None
        # ========== 录屏集成结束 ==========
        await spider.setup_driver()
        
        # ========== 窗口调整开始 ==========
        try:
            # 等待浏览器完全打开并加载页面
            await spider.page.wait_for_load_state('networkidle')
            logger.info("浏览器页面加载完成")
            
            # 获取自适应窗口尺寸
            window_width, window_height = get_adaptive_window_size()
            
            # 调整浏览器窗口位置和大小
            await spider.page.set_viewport_size({"width": window_width, "height": window_height})
            
            # 调整Anaconda Prompt窗口到右半屏
            anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
            split_window_to_right_half(anaconda_window_titles, show_debug=True)
            
            # 激活并置顶Anaconda Prompt窗口
            activate_window(anaconda_window_titles, show_debug=True)
            
            logger.info("Anaconda Prompt窗口已调整到右半屏并置顶")
        except Exception as e:
            logger.error(f"窗口调整失败: {str(e)}")
        # ========== 窗口调整结束 ==========
        
        await spider.handle_menu_items()
        zip_filepath, status = await spider.save_and_compress_data()
        logger.info("程序执行完成")
        if callback_url:
            await spider._send_callback(zip_filepath, callback_url, status)
        return zip_filepath, status
    except Exception as e:
        logger.error(f"程序异常: {str(e)}", exc_info=True)
        status = "500"
        if callback_url:
            await spider._send_callback(zip_filepath, callback_url, status)
        return None, status
    finally:
        if spider:
            await spider.cleanup()
        if screen_recorder:
            try:
                screen_recorder.stop_recording()
                logger.info(f"录屏已结束，文件: {mp4_path}")
            except Exception as e:
                logger.error(f"录屏结束失败: {str(e)}")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='中国电信资费查询爬虫')
    parser.add_argument('--provinces', type=str, help='省份编码',default='ah')
    parser.add_argument('--callback-url', type=str, help='回调URL',default=None)
    args = parser.parse_args()

    loop = asyncio.ProactorEventLoop()
    asyncio.set_event_loop(loop)
    try:
        result, status = loop.run_until_complete(main(args.provinces, args.callback_url))
        # 输出JSON格式的结果
        print(json.dumps({"zip_file_path": result, "status": status}))
    except Exception as e:
        # 即使发生错误也输出结果
        print(json.dumps({"error": str(e), "zip_file_path": None, "status": "500"}))
    finally:
        loop.close()

