<!-- Generated by SpecStory -->

# 遍历资费大类的逻辑修改 (2025-06-04 01:52Z)

_**User**_

<div data-v-59e8cb48="" class="nav-bar-content"><div data-v-59e8cb48="" class="nav_box sortList" style=""><span data-v-59e8cb48="" class="nav-name">资费大类:</span><ul data-v-59e8cb48="" class="nav-bar"><li data-v-59e8cb48="" class="nameActive"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">个人资费</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">政企资费</span></a></li></ul></div><div data-v-59e8cb48="" class="nav_box sortList"><span data-v-59e8cb48="" class="nav-name">资费来源:</span><ul data-v-59e8cb48="" class="nav-bar"><li data-v-59e8cb48="" class="nameActive"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全网资费</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">云南资费</span></a></li></ul></div><div data-v-59e8cb48="" class="nav_box sortList"><span data-v-59e8cb48="" class="nav-name">资费类型:</span><ul data-v-59e8cb48="" class="nav-bar"><li data-v-59e8cb48="" class="nameActive"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">套餐</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">加装包（不含港澳台和国际）</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">营销活动</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">国际及港澳台加装包</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">其他</span></a></li></ul></div><div data-v-59e8cb48="" class="ex_nav_box"><div data-v-59e8cb48="" class="nav_box sortList" style="border: none;"><span data-v-59e8cb48="" class="nav-name">资费系列名称:</span><div data-v-59e8cb48="" class="nav-other-box"><ul data-v-59e8cb48="" class="nav-bar nav-bar-hidden"><li data-v-59e8cb48="" class="nameActive"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全部</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">萌卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">宝藏卡29</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">宝藏卡39</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">孝心卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">动感地带联名卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">骑士卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享99</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享129</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享169</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享199</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享239</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享299</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享399</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全家享599</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">动感地带潮玩卡39</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">动感地带潮玩卡59</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">动感地带潮玩卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">动感地带芒果卡</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">5G智享套餐个人版</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">积木套餐</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">5GA套餐</span></a></li></ul></div></div><div data-v-59e8cb48="" class="expand-or-coll-btn"><span data-v-59e8cb48="" class="detail-btn-title">展开全部</span><div data-v-59e8cb48=""><span data-v-59e8cb48="" class="right"></span></div></div></div><div data-v-59e8cb48="" class="nav_box"><span data-v-59e8cb48="" class="nav-name">资费价格:</span><ul data-v-59e8cb48="" class="nav-bar"><li data-v-59e8cb48="" class="nameActive"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">全部</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">10以下</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">10-50</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">50-100</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">100-150</span></a></li><li data-v-59e8cb48="" class="nav-bar-list"><a data-v-59e8cb48="" href="javascript:;"><span data-v-59e8cb48="" class="text">150以上</span></a></li></ul></div><div data-v-59e8cb48="" class="nav_box custom-price"><span data-v-59e8cb48="" class="nav-name">自定义价格</span><div data-v-59e8cb48="" class="price-range"><div data-v-59e8cb48="" class="price-least"><input data-v-59e8cb48="" placeholder="最低价格" type="text" class="search-input"></div><div data-v-59e8cb48="" class="price-to">至</div><div data-v-59e8cb48="" class="price-max"><input data-v-59e8cb48="" placeholder="最高价格" type="text" class="search-input"></div></div></div></div>  现在发现部分身份有资费大类，需要在当前的逻辑上，先遍历资费大类，然后继续执行当前的逻辑

---

_**Assistant**_

我明白了，需要在现有的爬取逻辑中添加对资费大类的处理。我会修改代码以支持先遍历资费大类，然后再遍历资费来源和资费类型。让我来修改相关代码。

首先，我会修改 `find_navigation_boxes` 函数以支持获取资费大类导航框：


---

```diff
  
  def find_navigation_boxes(nav_content):
-     """查找资费来源和资费类型的导航框"""
+     """查找资费大类、资费来源和资费类型的导航框"""
      nav_boxes = nav_content.find_elements(By.CSS_SELECTOR, "div[data-v-59e8cb48].nav_box.sortList")
      if not nav_boxes:
          logger.error("未找到导航框元素，请检查页面结构")
-         return None, None
+         return None, None, None
          
+     category_box = None
      source_box = None
      type_box = None
      for box in nav_boxes:
          nav_name = box.find_element(By.CSS_SELECTOR, "span[data-v-59e8cb48].nav-name").text.strip()
-         if "资费来源" in nav_name:
+         if "资费大类" in nav_name:
+             category_box = box
+         elif "资费来源" in nav_name:
              source_box = box
          elif "资费类型" in nav_name:
```

```diff
      if not source_box or not type_box:
          logger.error("未找到资费来源或资费类型的导航框")
-         return None, None
+         return None, None, None
          
-     return source_box, type_box
+     return category_box, source_box, type_box
  
  def get_navigation_items(box):
```

```diff
          # 获取资费来源和资费类型的导航框
          nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
-         source_box, type_box = find_navigation_boxes(nav_content)
+         category_box, source_box, type_box = find_navigation_boxes(nav_content)
          
          if not source_box or not type_box:
              logger.error("未找到导航框，无法继续爬取")
              return None
-         
- 
- 
- 
- 
+         
          # 获取所有资费来源选项
          source_items = get_navigation_items(source_box)
```

---

现在我需要修改 `crawl_province` 函数以支持遍历资费大类：


---

```diff
          logger.debug("页面加载完成")
          
-         # 获取资费来源和资费类型的导航框
+         # 获取资费大类、资费来源和资费类型的导航框
          nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
          category_box, source_box, type_box = find_navigation_boxes(nav_content)
```

```diff
              return None
  
+         # 存储所有爬取的数据
+         all_data = []
+ 
+         # 获取资费大类选项（如果存在）
+         category_items = []
+         if category_box:
+             category_items = get_navigation_items(category_box)
+             logger.info(f"找到 {len(category_items)} 个资费大类选项")
+         else:
+             logger.info("未找到资费大类选项，将直接处理资费来源")
+ 
+         # 如果没有资费大类，创建一个虚拟的"全部"选项
+         if not category_items:
+             category_items = [None]
+ 
+         # 遍历每个资费大类（如果存在）
+         for category_item in category_items:
+             category_text = "全部"  # 默认值
+             if category_item:
+                 try:
+                     # 点击资费大类
+                     category_text = category_item.text.strip()
+                     driver.execute_script("arguments[0].click();", category_item)
+                     logger.debug(f"已选择资费大类: {category_text}")
+                     time.sleep(2)  # 等待页面加载
+                 except Exception as e:
+                     logger.error(f"选择资费大类 {category_text} 时发生错误: {str(e)}")
+                     continue
+ 
+             # 重新获取资费来源和资费类型选项
+             nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
+             _, source_box, type_box = find_navigation_boxes(nav_content)
+             if not source_box or not type_box:
+                 logger.error("重新获取资费来源或资费类型导航框失败")
+                 continue
+ 
              # 获取所有资费来源选项
              source_items = get_navigation_items(source_box)
              if not source_items:
                  logger.error("未找到资费来源选项")
-             return None
-             
-         # 存储所有爬取的数据
-         all_data = []
+                 continue
  
              # 遍历每个资费来源
```

```diff
                      driver.execute_script("arguments[0].click();", source_item)
                      logger.debug(f"已选择资费来源: {source_text}")
-                 time.sleep(2)  # 增加等待时间，确保页面完全加载
+                     time.sleep(2)  # 等待页面加载
  
                      # 重新获取资费类型选项
                      nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
-                 _, type_box = find_navigation_boxes(nav_content)
+                     _, _, type_box = find_navigation_boxes(nav_content)
                      if not type_box:
                          logger.error("重新获取资费类型导航框失败")
```

```diff
                              driver.execute_script("arguments[0].click();", type_item)
                              logger.debug(f"已选择资费类型: {type_text}")
-                         time.sleep(2)  # 增加等待时间，确保页面完全加载
+                             time.sleep(2)  # 等待页面加载
  
                              # 处理资费项目
                              items_data = process_tariff_items(driver, wait, source_text, type_text)
                              if items_data:
+                                 # 添加资费大类信息到数据中
+                                 for item in items_data:
+                                     item["资费大类"] = category_text
                                  all_data.extend(items_data)
-                             logger.info(f"成功获取 {len(items_data)} 条{source_text}-{type_text}的资费数据")
+                                 logger.info(f"成功获取 {len(items_data)} 条{category_text}-{source_text}-{type_text}的资费数据")
                              else:
-                             logger.warning(f"未获取到{source_text}-{type_text}的资费数据")
+                                 logger.warning(f"未获取到{category_text}-{source_text}-{type_text}的资费数据")
  
                          except Exception as e:
```

```diff
                                  # 重新点击资费来源
                                  nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
-                             source_box, _ = find_navigation_boxes(nav_content)
+                                 _, source_box, _ = find_navigation_boxes(nav_content)
                                  if source_box:
                                      source_items = get_navigation_items(source_box)
```

```diff
              return None
  
-         # 保存数据到Excel，现在会返回多个文件路径
+         # 保存数据到Excel
          excel_paths = save_to_excel(all_data, province_name)
          if not excel_paths:
              logger.error("保存Excel文件失败")
              return None
  
-         # 创建zip文件，包含所有Excel文件，并传入省份名称
+         # 创建zip文件
          zip_path = make_zip(excel_paths, province_name)
          if not zip_path:
```

---

我已经完成了代码修改，主要做了以下更改：

1. 修改了 `find_navigation_boxes` 函数，使其支持查找资费大类导航框，现在返回三个值：category_box（资费大类）、source_box（资费来源）和 type_box（资费类型）。

2. 修改了 `crawl_province` 函数，添加了对资费大类的处理逻辑：
   - 首先检查是否存在资费大类导航框
   - 如果存在资费大类，获取所有资费大类选项并遍历
   - 如果不存在资费大类，创建一个虚拟的"全部"选项
   - 在遍历资费来源和资费类型之前，先选择资费大类
   - 在数据中添加"资费大类"字段，记录当前选择的资费大类
   - 优化了日志输出，现在会显示完整的资费大类-资费来源-资费类型路径

3. 优化了错误处理和页面刷新逻辑，确保在切换资费大类后能正确重新获取资费来源和资费类型选项。

这些修改使得爬虫能够：
1. 正确处理有资费大类的省份
2. 兼容没有资费大类的省份
3. 在数据中记录资费大类信息
4. 保持原有的资费来源和资费类型处理逻辑

现在爬虫会按照以下顺序处理数据：
1. 资费大类（如果存在）
2. 资费来源
3. 资费类型
4. 具体的资费项目

每个资费项目的数据中都会包含资费大类信息，这样可以更好地组织和分析数据。

---

_**User**_

存在部分省份没有资费大类的结构

---

_**Assistant**_

我明白了，需要优化代码以更好地处理部分省份没有资费大类的情况。我会修改代码，使其能够智能判断是否存在资费大类，并相应地调整爬取逻辑。


---

```diff
          logger.debug("页面加载完成")
          
-         # 获取资费大类、资费来源和资费类型的导航框
+         # 获取导航框
          nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
          category_box, source_box, type_box = find_navigation_boxes(nav_content)
          
          if not source_box or not type_box:
-             logger.error("未找到导航框，无法继续爬取")
+             logger.error("未找到资费来源或资费类型导航框，无法继续爬取")
              return None
  
          # 存储所有爬取的数据
          all_data = []
  
-         # 获取资费大类选项（如果存在）
-         category_items = []
+         # 检查是否存在资费大类
+         has_category = False
          if category_box:
+             try:
+                 # 尝试获取资费大类选项
                  category_items = get_navigation_items(category_box)
+                 if category_items and len(category_items) > 0:
+                     has_category = True
                      logger.info(f"找到 {len(category_items)} 个资费大类选项")
                  else:
-             logger.info("未找到资费大类选项，将直接处理资费来源")
- 
-         # 如果没有资费大类，创建一个虚拟的"全部"选项
-         if not category_items:
-             category_items = [None]
- 
-         # 遍历每个资费大类（如果存在）
+                     logger.info(f"{province_name}没有资费大类选项")
+             except Exception as e:
+                 logger.warning(f"检查资费大类时发生错误: {str(e)}，将按无资费大类处理")
+ 
+         if has_category:
+             # 有资费大类的情况，遍历每个资费大类
+             category_items = get_navigation_items(category_box)
              for category_item in category_items:
-             category_text = "全部"  # 默认值
-             if category_item:
                  try:
                      # 点击资费大类
```

```diff
                      logger.debug(f"已选择资费大类: {category_text}")
                      time.sleep(2)  # 等待页面加载
+ 
+                     # 处理当前资费大类下的数据
+                     category_data = process_category_data(driver, wait, category_text, source_box, type_box)
+                     if category_data:
+                         all_data.extend(category_data)
+                         logger.info(f"成功获取 {len(category_data)} 条{category_text}的资费数据")
+                     else:
+                         logger.warning(f"未获取到{category_text}的资费数据")
+ 
                  except Exception as e:
-                     logger.error(f"选择资费大类 {category_text} 时发生错误: {str(e)}")
+                     logger.error(f"处理资费大类 {category_text} 时发生错误: {str(e)}")
                      continue
- 
+         else:
+             # 没有资费大类的情况，直接处理资费来源和类型
+             logger.info(f"{province_name}没有资费大类，直接处理资费来源和类型")
+             category_data = process_category_data(driver, wait, "全部", source_box, type_box)
+             if category_data:
+                 all_data.extend(category_data)
+                 logger.info(f"成功获取 {len(category_data)} 条资费数据")
+             else:
+                 logger.warning(f"未获取到{province_name}的资费数据")
+ 
+         if not all_data:
+             logger.warning(f"未获取到{province_name}的任何资费数据")
+             return None
+ 
+         # 保存数据到Excel
+         excel_paths = save_to_excel(all_data, province_name)
+         if not excel_paths:
+             logger.error("保存Excel文件失败")
+             return None
+ 
+         # 创建zip文件
+         zip_path = make_zip(excel_paths, province_name)
+         if not zip_path:
+             logger.error("创建zip文件失败")
+             return None
+ 
+         logger.info(f"成功爬取{province_name}的资费信息，共{len(all_data)}条数据")
+         return zip_path
+ 
+     except Exception as e:
+         logger.error(f"爬取{province_name}时发生错误: {str(e)}")
+         return None
+ 
+ def process_category_data(driver: webdriver.Chrome,
+                          wait: WebDriverWait,
+                          category_text: str,
+                          source_box: Any,
+                          type_box: Any) -> List[Dict]:
+     """处理特定资费大类下的数据（或没有资费大类时的数据）
+     
+     Args:
+         driver: WebDriver实例
+         wait: WebDriverWait实例
+         category_text: 资费大类文本
+         source_box: 资费来源导航框
+         type_box: 资费类型导航框
+         
+     Returns:
+         List[Dict]: 处理后的数据列表
+     """
+     category_data = []
+     
+     try:
          # 重新获取资费来源和资费类型选项
          nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
          _, source_box, type_box = find_navigation_boxes(nav_content)
          if not source_box or not type_box:
              logger.error("重新获取资费来源或资费类型导航框失败")
-                 continue
+             return []
  
          # 获取所有资费来源选项
          source_items = get_navigation_items(source_box)
          if not source_items:
              logger.error("未找到资费来源选项")
-                 continue
+             return []
  
          # 遍历每个资费来源
```

```diff
                              for item in items_data:
                                  item["资费大类"] = category_text
-                                 all_data.extend(items_data)
+                             category_data.extend(items_data)
                              logger.info(f"成功获取 {len(items_data)} 条{category_text}-{source_text}-{type_text}的资费数据")
                          else:
```

```diff
                  continue
  
-         if not all_data:
-             logger.warning(f"未获取到{province_name}的任何资费数据")
-             return None
- 
-         # 保存数据到Excel
-         excel_paths = save_to_excel(all_data, province_name)
-         if not excel_paths:
-             logger.error("保存Excel文件失败")
-             return None
- 
-         # 创建zip文件
-         zip_path = make_zip(excel_paths, province_name)
-         if not zip_path:
-             logger.error("创建zip文件失败")
-             return None
- 
-         logger.info(f"成功爬取{province_name}的资费信息，共{len(all_data)}条数据")
-         return zip_path
+         return category_data
  
      except Exception as e:
-         logger.error(f"爬取{province_name}时发生错误: {str(e)}")
-         return None
+         logger.error(f"处理资费大类 {category_text} 数据时发生错误: {str(e)}")
+         return []
  
  def close_browser(driver: Optional[webdriver.Chrome]) -> None:
```

---

我已经对代码进行了优化，主要做了以下改进：

1. 更智能的资费大类检测：
   - 添加了 `has_category` 标志来明确标识是否存在资费大类
   - 通过尝试获取资费大类选项并检查其数量来判断是否真实存在资费大类
   - 增加了异常处理，当检查资费大类出错时，会按无资费大类处理

2. 代码结构优化：
   - 将资费来源和资费类型的处理逻辑抽取到新的 `process_category_data` 函数中
   - 使代码结构更清晰，便于维护和调试
   - 减少了代码重复

3. 更灵活的处理逻辑：
   - 有资费大类时：遍历每个资费大类，然后处理其下的资费来源和类型
   - 无资费大类时：直接处理资费来源和类型，使用"全部"作为资费大类标识
   - 统一的数据处理流程，确保数据格式一致

4. 更好的错误处理和日志：
   - 添加了更详细的日志信息，包括省份名称
   - 优化了错误处理逻辑，确保在出错时能够继续处理其他数据
   - 添加了更多的状态检查和验证

5. 数据一致性：
   - 无论是否有资费大类，都会在数据中包含"资费大类"字段
   - 对于没有资费大类的省份，使用"全部"作为默认值
   - 保持了数据的统一格式

这些改进使得爬虫能够：
1. 自动识别省份是否有资费大类
2. 正确处理有资费大类和没有资费大类的省份
3. 保持数据格式的一致性
4. 提供更详细的日志信息
5. 更健壮的错误处理

现在爬虫可以更好地处理不同省份的页面结构差异，同时保持了数据的一致性和完整性。

---

_**User**_

生成的xlxs文件命名要修改成  如果有资费大类，省份名称_mobile_随机数_{资费大类}_{资费来源}_{资费类型}.xlsx，如果没有资费大类使用当前的命名规则，最后将当次生成的xlxs压缩成zip

---

_**Assistant**_

我明白了，需要根据是否有资费大类来调整Excel文件的命名规则。我会修改 `save_to_excel` 函数来实现这个需求。


---

```diff
              for data_dict in data_list:
                  source = data_dict.get("资费来源", "未知来源")
-                 if source not in source_groups:
-                     source_groups[source] = []
-                 source_groups[source].append(data_dict)
+                 type_text = data_dict.get("资费类型", "未知类型")
+                 category = data_dict.get("资费大类", "全部")
+                 
+                 # 创建复合键，包含资费大类、来源和类型
+                 group_key = (category, source, type_text)
+                 if group_key not in source_groups:
+                     source_groups[group_key] = []
+                 source_groups[group_key].append(data_dict)
  
              saved_files = []
```

```diff
              safe_province = province_name.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
              
-             # 为每个资费来源生成单独的Excel文件
-             for source, group_data in source_groups.items():
+             # 为每个分组生成单独的Excel文件
+             for (category, source, type_text), group_data in source_groups.items():
                  # 生成文件名
+                 safe_category = category.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
                  safe_source = source.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
+                 safe_type = type_text.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
+                 
+                 # 根据是否有资费大类使用不同的命名规则
+                 if category != "全部":  # 有资费大类
+                     filename = f"{safe_province}_mobile_{rand_str}_{safe_category}_{safe_source}_{safe_type}.xlsx"
+                 else:  # 没有资费大类，使用原有命名规则
                      filename = f"{safe_province}_mobile_{rand_str}_{safe_source}.xlsx"
+                     
                  file_path = self.data_dir / filename
  
```

---

我已经修改了 `save_to_excel` 函数，主要做了以下改进：

1. 数据分组逻辑改进：
   - 使用复合键 `(category, source, type_text)` 来分组数据
   - 同时考虑资费大类、资费来源和资费类型进行分组
   - 确保相同资费大类、来源和类型的数据被分到同一组

2. 文件命名规则：
   - 有资费大类时：`省份名称_mobile_随机数_{资费大类}_{资费来源}_{资费类型}.xlsx`
   - 无资费大类时：保持原有规则 `省份名称_mobile_随机数_{资费来源}.xlsx`
   - 所有特殊字符都会被安全处理（移除或替换）

3. 安全性处理：
   - 对所有用于文件名的字段进行安全处理，移除或替换特殊字符
   - 使用 `safe_category`、`safe_source` 和 `safe_type` 确保文件名合法

4. 数据完整性：
   - 保持原有的数据补齐和空键处理逻辑
   - 确保所有数据字段的一致性

现在，当爬取有资费大类的省份时，会生成类似这样的文件名：
```
北京_mobile_123456_个人资费_全网资费_套餐.xlsx
北京_mobile_123456_政企资费_全网资费_加装包.xlsx
```

当爬取没有资费大类的省份时，会保持原有的命名规则：
```
上海_mobile_123456_全网资费.xlsx
上海_mobile_123456_云南资费.xlsx
```

所有生成的Excel文件最后会被打包到同一个zip文件中，保持原有的压缩逻辑不变。这样的改进可以：
1. 更清晰地区分不同资费类型的数据
2. 保持向后兼容性（对没有资费大类的省份）
3. 提供更有组织的数据文件结构
4. 便于后续数据分析和处理

---

