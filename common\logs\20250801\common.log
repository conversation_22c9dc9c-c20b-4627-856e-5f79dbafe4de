2025-08-01 10:38:05 - MainProcess - 19432 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:25:00 - MainProcess - 1012 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:25:01 - SpawnProcess-1 - 51736 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:39:11 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:39:11 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (128, 128)
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 11:39:14 - SpawnProcess-1 - 51736 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 11:44:34 - MainProcess - 45792 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:44:35 - SpawnProcess-1 - 55228 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:44:40 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:44:40 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (128, 128)
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 11:44:43 - SpawnProcess-1 - 55228 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 11:47:24 - MainProcess - 22132 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:47:31 - MainProcess - 30912 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:47:59 - MainProcess - 45592 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:48:04 - MainProcess - 4500 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:48:06 - SpawnProcess-1 - 7056 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:49:11 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:49:11 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (128, 128)
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 11:49:15 - SpawnProcess-1 - 7056 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 11:52:37 - MainProcess - 14220 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:54:02 - MainProcess - 56832 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:54:03 - SpawnProcess-1 - 54888 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 11:54:07 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:54:07 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (224, 224)
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 11:54:10 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 16:31:24 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaMobile\zip\20250801\北京_mobile_226644_资费数据.mp4
2025-08-01 16:31:25 - SpawnProcess-1 - 54888 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 411713 字节
2025-08-01 16:31:27 - SpawnProcess-1 - 54888 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: Cannot connect to host ************:8082 ssl:default [Connect call failed ('************', 8082)]
2025-08-01 16:35:05 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 16:35:05 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 16:35:05 - SpawnProcess-1 - 54888 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 16:35:07 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 16:35:07 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 16:35:11 - SpawnProcess-1 - 54888 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 16:40:03 - MainProcess - 55532 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 16:40:52 - MainProcess - 32644 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 16:40:54 - SpawnProcess-1 - 46056 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 16:40:55 - SpawnProcess-1 - 46056 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-01 16:40:55 - SpawnProcess-1 - 46056 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-01 16:40:55 - SpawnProcess-1 - 46056 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-01 16:40:56 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 16:40:56 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (245, 227)
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 16:41:00 - SpawnProcess-1 - 46056 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 16:57:21 - MainProcess - 13224 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 17:10:40 - MainProcess - 37952 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 17:10:41 - SpawnProcess-1 - 61344 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 17:10:46 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 17:10:46 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (96, 96)
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 17:10:49 - SpawnProcess-1 - 61344 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-01 17:21:36 - MainProcess - 3336 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 17:21:37 - SpawnProcess-1 - 48296 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250801
2025-08-01 17:21:40 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-01 17:21:40 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (224, 224)
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-01 17:21:43 - SpawnProcess-1 - 48296 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
