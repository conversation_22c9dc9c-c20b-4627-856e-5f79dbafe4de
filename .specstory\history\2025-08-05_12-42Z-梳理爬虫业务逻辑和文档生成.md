<!-- Generated by SpecStory -->

# 梳理爬虫业务逻辑和文档生成 (2025-08-05 12:42Z)

_**User**_

理解这几个爬虫逻辑代码，现在客户需要梳理一份爬虫的业务逻辑，重点需要说明清楚页面的爬取方式和等待时间，分不通的运营商，生成md文档

---

_**Assistant**_

我来帮您分析这几个爬虫代码的业务逻辑，并生成一份详细的MD文档。让我先仔细分析这些代码的结构和逻辑。


---



---

# 运营商爬虫业务逻辑分析文档

## 概述

本文档详细分析了三个不同运营商（中国广电、中国移动、中国电信）的爬虫业务逻辑，重点说明页面爬取方式和等待时间策略。

## 1. 中国广电爬虫 (ChinaBroadnet)

### 1.1 技术栈
- **自动化框架**: Selenium WebDriver
- **浏览器**: Chrome
- **编程语言**: Python
- **日志系统**: 自定义logger配置

### 1.2 核心业务逻辑

#### 页面访问流程
1. **初始化浏览器**
   ```python
   def setup_driver():
       chrome_options = Options()
       chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
       # 随机用户代理
       user_agents = [
           'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
           'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101...'
       ]
       chrome_options.add_argument(f'user-agent={random.choice(user_agents)}')
   ```

2. **访问目标网站**
   - URL: `https://m.10099.com.cn/expensesNotice/#/home`
   - 等待页面加载完成

#### 页面爬取方式

**省份选择机制**:
- 使用级联选择器 (`van-cascader`)
- 通过下拉箭头图标触发省份选择
- 支持指定省份或全量爬取

**数据提取策略**:
```python
def extract_tariff_data(driver):
    # 多种选择器策略
    sec_box_selectors = [
        (By.CLASS_NAME, "secBox"),
        (By.XPATH, "//section[contains(@class, 'secBox')]"),
        (By.XPATH, "//div[contains(@class, 'el-table')]"),
        # ... 更多选择器
    ]
```

**标签页处理**:
- 支持带标签页的数据结构（基础业务、其它业务）
- 处理跨行单元格数据 (`rowspan`)
- 提取详细信息

### 1.3 等待时间策略

**随机等待函数**:
```python
def random_sleep(min_seconds=2, max_seconds=5):
    sleep_time = random.uniform(min_seconds, max_seconds)
    time.sleep(sleep_time)
```

**关键等待节点**:
- 页面加载: 2-4秒随机等待
- 下拉菜单操作: 1-2秒
- 选项选择后: 4-6秒
- 页面刷新后: 3-5秒
- 数据加载: 2-3秒额外等待

**页面加载检测**:
```python
def wait_for_page_load(driver, timeout=10):
    WebDriverWait(driver, timeout).until(
        lambda d: d.execute_script('return document.readyState') == 'complete'
    )
```

### 1.4 错误处理和重试机制

**重试策略**:
- 最大重试次数: 10次
- 重试间隔: 5秒
- 支持页面刷新恢复

**数据获取失败处理**:
```python
# 切换到其他下拉选项再切回重试，最多3次
retry_count = 0
max_retries = 3
while retry_count < max_retries and not data:
    # 随机选择其他选项
    # 切换后再次尝试
```

## 2. 中国移动爬虫 (ChinaMobile)

### 2.1 技术栈
- **自动化框架**: Selenium WebDriver
- **浏览器**: Chrome
- **数据格式**: Excel + ZIP
- **配置管理**: 数据类 (dataclass)

### 2.2 核心业务逻辑

#### 浏览器配置
```python
@dataclass
class ChromeOptions:
    headless: bool = False
    incognito: bool = True
    disable_gpu: bool = True
    disable_extensions: bool = True
    disable_popup_blocking: bool = True
    disable_cache: bool = True
    user_agent: Optional[str] = None
    proxy: Optional[str] = None
    window_size: Optional[tuple] = None
```

#### 数据管理策略
```python
class DataManager:
    def save_to_excel(self, data_list, province_name, filename=None):
        # 保存为Excel格式
        
    def make_zip(self, files, province_name, zip_name=None):
        # 创建ZIP压缩包
```

### 2.3 页面爬取方式

**省份导航处理**:
```python
def find_navigation_boxes(nav_content):
    # 查找导航框
    boxes = nav_content.find_elements(By.CLASS_NAME, "navigation-box")
```

**数据提取流程**:
1. 处理提示信息 (`extract_tips_data`)
2. 提取表格数据 (`extract_table_data`)
3. 处理灰色区域数据 (`extract_gray_data`)

### 2.4 等待时间策略

**滚动等待机制**:
```python
def scroll_to_bottom(driver, max_wait_time=30):
    # 滚动到页面底部，最大等待30秒
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        # 滚动逻辑
```

**元素等待**:
- 使用 `WebDriverWait` 等待元素出现
- 超时时间: 10-30秒不等

## 3. 中国电信爬虫 (ChinaTelecom)

### 3.1 技术栈
- **自动化框架**: Playwright
- **浏览器**: Chromium
- **异步处理**: asyncio
- **数据格式**: JSON + ZIP

### 3.2 核心业务逻辑

#### 浏览器初始化
```python
async def setup_driver(self) -> None:
    self.browser = await playwright.chromium.launch(
        headless=False,
        args=[
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions',
            '--no-sandbox'
        ]
    )
```

#### 页面处理策略
**菜单项处理**:
```python
async def handle_menu_items(self) -> None:
    # 处理菜单项点击
    # 支持新页面打开
    # 异步等待页面加载
```

**网络请求拦截**:
```python
async def _handle_network_request_content(self) -> None:
    # 拦截网络请求
    # 保存API响应
    # 处理动态内容
```

### 3.3 页面爬取方式

**多页面处理**:
- 支持新标签页打开
- 异步处理多个页面
- 页面上下文管理

**数据解析**:
```python
async def _parse_product_data(self, product) -> dict:
    # 解析产品数

---

