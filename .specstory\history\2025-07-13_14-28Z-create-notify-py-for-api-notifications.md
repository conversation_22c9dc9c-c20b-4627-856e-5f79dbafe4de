<!-- Generated by SpecStory -->

# Create notify.py for API notifications (2025-07-13 14:28Z)

_**User**_

post  http://192.168.101.21:8088/callback/notify
{
    "provinceName": "", //省份名称
    "entName": "", //运营商名称
    "tariffType": "", //资费类型
    "errMsg": "" //描述信息
}  在common文件夹中新加一个notify.py文件 ，调用接口进行通知。封装一个方法，提供给爬虫的引擎调用

---

_**Assistant**_

我会在 `common` 文件夹下新建一个 `notify.py` 文件，封装一个用于 POST 通知的函数。该函数会向指定的接口（http://192.168.101.21:8088/callback/notify）发送包含 `provinceName`、`entName`、`tariffType` 和 `errMsg` 的 JSON 数据。爬虫引擎可直接调用此方法进行通知。

具体实现思路如下：

- 使用 `requests` 库进行 HTTP POST。
- 封装一个如 `notify_callback(province_name, ent_name, tariff_type, err_msg)` 的方法。
- 方法内部构造 JSON 并发送请求，返回响应或异常信息。
- 提供简单的日志输出（如有需要可集成现有 logger）。

接下来我将创建该文件并实现上述功能。

---

```diff
- 
+ import requests
+ 
+ def notify_callback(province_name: str, ent_name: str, tariff_type: str, err_msg: str) -> bool:
+     """
+     向 http://192.168.101.21:8088/callback/notify 发送通知。
+     :param province_name: 省份名称
+     :param ent_name: 运营商名称
+     :param tariff_type: 资费类型
+     :param err_msg: 描述信息
+     :return: 是否通知成功
+     """
+     url = "http://192.168.101.21:8088/callback/notify"
+     payload = {
+         "provinceName": province_name,
+         "entName": ent_name,
+         "tariffType": tariff_type,
+         "errMsg": err_msg
+     }
+     try:
+         response = requests.post(url, json=payload, timeout=5)
+         response.raise_for_status()
+         return True
+     except Exception as e:
+         print(f"[notify_callback] Failed to notify: {e}")
+         return False 
```

---

`common/notify.py` 已创建，包含 `notify_callback` 方法，可供爬虫引擎调用，向指定接口发送通知。

如需在爬虫中使用，只需：
```python
from common.notify import notify_callback

# 示例调用
notify_callback("广东省", "中国移动", "套餐A", "抓取成功")
```

如需集成日志或异常处理，可随时告知！

---

_**User**_

日志需要记录

---

_**Assistant**_

根据你的要求和项目规则，所有通知操作都应记录日志，包括成功和失败的情况。你项目已实现了多运营商的日志实例（如 `telecom_logger`、`mobile_logger`、`unicom_logger`、`broadnet_logger` 以及默认的 `logger`），这些都定义在 `common/logger_config.py`。

最佳实践如下：
- 在 `notify_callback` 方法中，调用合适的 logger 记录每次通知的详细信息（包括参数、响应、异常等）。
- 日志级别建议：成功用 `info`，失败用 `error`。
- 可根据调用方传递运营商名称，选择对应 logger，否则用默认 `logger`。

我将对 `common/notify.py` 进行如下增强：
- 支持自动选择 logger（如 `ent_name` 匹配到运营商，否则用默认）。
- 记录每次通知的参数、响应状态、异常等详细日志。

接下来我会实现这些改动。

---

Read file: common/notify.py

---

```diff
  import requests
+ from common.logger_config import telecom_logger, mobile_logger, unicom_logger, broadnet_logger, logger
  
  def notify_callback(province_name: str, ent_name: str, tariff_type: str, err_msg: str) -> bool:
      """
-     向 http://192.168.101.21:8088/callback/notify 发送通知。
+     向 http://192.168.101.21:8088/callback/notify 发送通知，并记录日志。
      :param province_name: 省份名称
      :param ent_name: 运营商名称
```

```diff
          "errMsg": err_msg
      }
+     # 选择合适的 logger
+     ent_name_lower = (ent_name or '').lower()
+     if '电信' in ent_name_lower or 'telecom' in ent_name_lower:
+         log = telecom_logger
+     elif '移动' in ent_name_lower or 'mobile' in ent_name_lower:
+         log = mobile_logger
+     elif '联通' in ent_name_lower or 'unicom' in ent_name_lower:
+         log = unicom_logger
+     elif '广电' in ent_name_lower or 'broadnet' in ent_name_lower:
+         log = broadnet_logger
+     else:
+         log = logger
+     log.info(f"[notify_callback] 通知参数: {payload}")
      try:
          response = requests.post(url, json=payload, timeout=5)
          response.raise_for_status()
+         log.info(f"[notify_callback] 通知成功，响应: {response.status_code} {response.text}")
          return True
      except Exception as e:
-         print(f"[notify_callback] Failed to notify: {e}")
+         log.error(f"[notify_callback] 通知失败: {e}")
          return False 
```

---

已增强 `notify_callback` 方法，现会自动选择对应运营商的 logger，并详细记录每次通知的参数、响应和异常信息。这样可确保所有通知操作均有日志可查，便于后续排查和审计。

如需进一步自定义日志内容或格式，欢迎随时告知！

---

