<!-- Generated by SpecStory -->

# Update Logger to Use Common Module (2025-05-23 08:21Z)

_**User**_

logger改成使用common中的

---

_**Assistant**_

I'll help you modify the code to use the logger from the common module. First, let me analyze the current code and make the necessary changes.

I see that in `broadnet_crawler.py`, the logger is currently imported from a local `logger_config.py` file. We need to change this to use the logger from the common module instead.

Let me make this change:


---

```diff
  import zipfile
  from datetime import datetime
- from logger_config import logger
+ from common.logger_config import broadnet_logger as logger
  
  # ==================== Browser Setup and Configuration ====================
```

---

I've made the following change:
1. Replaced the import statement `from logger_config import logger` with `from common.logger_config import broadnet_logger as logger`

This change will now use the broadnet-specific logger from the common module instead of the local logger. The broadnet logger is specifically configured for the China Broadnet crawler with:
- Proper operator name ('ChinaBroadnet')
- Correct log directory structure
- Appropriate log file naming
- Consistent formatting with other operators' logs

The rest of the code can remain unchanged since we're aliasing `broadnet_logger` as `logger`, which maintains compatibility with all the existing logging calls in the code.

Is there anything else you'd like me to help you with?

---

