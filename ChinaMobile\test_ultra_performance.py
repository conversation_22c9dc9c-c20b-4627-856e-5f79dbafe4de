#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超高效性能测试脚本 - 测试激进优化效果
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from mobile_crawler import process_tariff_items, setup_driver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from common.logger_config import mobile_logger as logger

def test_ultra_performance():
    """测试超高效性能"""
    logger.info("开始超高效性能测试...")
    
    try:
        # 初始化浏览器
        driver = setup_driver()
        wait = WebDriverWait(driver, 60)
        
        # 访问测试页面（使用北京作为测试）
        test_url = "https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov=100"
        driver.get(test_url)
        
        # 等待页面加载
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "tariff-free")))
        logger.info("页面加载完成，开始超高效性能测试")
        
        start_time = time.time()
        
        # 执行解析测试
        result = process_tariff_items(
            driver=driver,
            wait=wait,
            source_text="测试来源",
            type_text="测试类型",
            province_id="100",
            province_name="北京"
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(f"超高效处理结果:")
        logger.info(f"处理时间: {processing_time:.2f} 秒")
        logger.info(f"解析数据条数: {len(result) if result else 0}")
        if result and processing_time > 0:
            avg_time = processing_time / len(result)
            logger.info(f"平均处理速度: {len(result)/processing_time:.2f} 条/秒")
            logger.info(f"平均每条数据处理时间: {avg_time:.3f} 秒")
            
            # 性能评级
            if avg_time < 0.1:
                logger.info("🚀 性能评级: 优秀")
            elif avg_time < 0.5:
                logger.info("✅ 性能评级: 良好")
            elif avg_time < 1.0:
                logger.info("⚠️  性能评级: 一般")
            else:
                logger.warning("❌ 性能评级: 较差")
        
        logger.info("\n=== 超高效性能测试完成 ===")
        
    except Exception as e:
        logger.error(f"超高效性能测试失败: {str(e)}")
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    test_ultra_performance() 