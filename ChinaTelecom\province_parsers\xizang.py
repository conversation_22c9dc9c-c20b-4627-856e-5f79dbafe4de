from playwright.async_api import Page
from common.logger_config import telecom_logger as logger
import asyncio

async def parse_xizang_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析西藏资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析西藏{menu_type}的页面内容...")
        
        # 等待列表区域加载
        await page.wait_for_selector("div.list-box", timeout=10000)
        
        # 使用PageDown键滚动加载所有内容
        logger.info("开始使用PageDown键滚动加载所有内容...")
        no_change_count = 0  # 记录数据条数不变的次数
        last_count = 0  # 上次滚动后的数据条数
        max_no_change = 3  # 连续3次数据不变则认为加载完成
        
        while True:  # 不限制滚动次数
            try:
                # 先向上滚动一段距离
                for _ in range(1):
                    await page.keyboard.press("PageUp")
                    await asyncio.sleep(0.3)
                logger.info("向上滚动完成")
                
                # 再向下滚动
                for _ in range(100):  # 减少每次滚动的次数，增加稳定性
                    await page.keyboard.press("PageDown")
                    await asyncio.sleep(0.2)  # 每次滚动后短暂等待
                logger.info("向下滚动完成")
                
                # 等待新内容加载
                await asyncio.sleep(0.2)
                
                # 获取当前数据条数
                current_products = await page.query_selector_all("div.list-box > div")
                current_count = len(current_products)
                logger.info(f"当前数据条数: {current_count}")
                
                # 检查数据条数是否变化
                if current_count == last_count:
                    no_change_count += 1
                    logger.info(f"数据条数未变化，连续 {no_change_count} 次")
                    if no_change_count >= max_no_change:  # 连续3次数据条数不变，认为加载完成
                        logger.info("数据条数连续3次未变化，认为加载完成")
                        break
                else:
                    no_change_count = 0  # 重置计数器
                    logger.info(f"数据条数增加: {current_count - last_count}")
                
                last_count = current_count
                
                # 每10次循环暂停一下，避免过快
                if no_change_count % 10 == 0:
                    await asyncio.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"滚动加载过程中发生错误: {str(e)}")
                # 如果发生错误，等待页面稳定
                await asyncio.sleep(2)
                continue
        
        # 滚动回顶部
        await page.keyboard.press("Home")
        await asyncio.sleep(0.2)
        
        # 获取所有产品元素
        product_elements = await page.query_selector_all("div.list-box > div")
        logger.info(f"开始解析页面内容，共 {len(product_elements)} 个产品")
        
        # 存储解析后的产品数据
        products_data = []
        
        # 遍历处理每个产品
        for index, product_element in enumerate(product_elements, 1):
            try:
                logger.info(f"开始解析第 {index}/{len(product_elements)} 个产品")
                
                # 获取产品标题和方案号
                title_element = await product_element.query_selector("div.top.clearfix > p.fl.title")
                code_element = await product_element.query_selector("div.top.clearfix > p.fr.small")
                title = await title_element.text_content() if title_element else ""
                code = await code_element.text_content() if code_element else ""
                code = code.replace("方案号：", "").strip() if code else ""
                
                # 获取基本信息
                basic_info = {}
                basic_info_elements = await product_element.query_selector_all("div.text > div.clearfix > div > p")
                for info_element in basic_info_elements:
                    info_text = await info_element.text_content()
                    if "：" in info_text:
                        key, value = info_text.split("：", 1)
                        basic_info[key.strip()] = value.strip()
                
                # 获取服务内容
                service_content = {}
                service_table = await product_element.query_selector("div.table-out > table")
                if service_table:
                    headers = await service_table.query_selector_all("th")
                    values = await service_table.query_selector_all("tr:last-child > td")
                    
                    for header, value in zip(headers, values):
                        header_text = await header.text_content()
                        value_text = await value.text_content()
                        service_content[header_text.strip()] = value_text.strip()
                
                # 获取备注信息
                remark_element = await product_element.query_selector("div.remark > p.p2")
                remark = await remark_element.text_content() if remark_element else ""
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{len(product_elements)} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_xizang_menu_config() -> dict:
    """获取西藏的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.w-13.menu-content",
        'menu_selector': "div.w-13.menu-content > div.top.clearfix > div",
        'list_selector': "div.list-box > div"
    } 