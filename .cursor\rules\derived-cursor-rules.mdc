---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## AI Coding Assistant Rules

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

All projects must be documented using markdown.  The documentation should include a clear description of the project goals, architecture, and implementation details.  Version updates should be noted in this section and relevant sections below. A `logs` directory will be automatically created to store log files generated by the China Telecom web scraping script. Log files will be named `telecom_YYYYMMDD.log` and a maximum of 5 log files will be retained.  Added 2025-05-14:  Detailed logging is required for all web scraping activities, including error handling and successful request details.  Added 2025-05-19:  Save HTML content for debugging purposes. Save the HTML content to `telecom_homepage.html`. Added 2025-05-19: Save screenshots of the page for debugging purposes.

## TECH STACK

- Python 3.x
- Selenium
- ChromeDriver (matching your Chrome browser version)
- `logging` module (for logging)
- `requests` module (for checking HTTP status codes)


## CODING STANDARDS

- Code must be well-commented and easy to understand.
- Code should follow PEP 8 style guidelines.
- All functions should have docstrings.
- Error handling should be implemented using `try-except` blocks.  Logs should be recorded using the `logger` object defined in `logger_config.py`.


## WORKFLOW & RELEASE RULES

- All code changes should be committed to Git.
- All commits should have clear and concise messages.
- Code reviews should be conducted before merging any code changes.
- Releases should be tagged with semantic versioning (e.g., v1.0.0).


## DEBUGGING

- Use the `logger` object defined in `logger_config.py` to log debugging information. This includes debug, info, warning, error and critical level logs. The logger should be configured to output to both the console and a log file.
- Implement comprehensive error handling to catch and log exceptions.
- When debugging Selenium scripts, consider using multiple selectors to locate elements and implementing robust waiting mechanisms (`WebDriverWait`) to handle dynamic website content and potential timeouts. Include multiple methods for locating elements (By.CLASS_NAME, By.XPATH, By.LINK_TEXT, etc). Use JavaScript injection to modify the `navigator.webdriver` property to help avoid detection by anti-scraping mechanisms. Use random delays (`random_sleep` function) to mimic human behavior. Consider using a headless browser to reduce the chance of detection.  Added 2025-05-14: Implement a robust waiting mechanism that handles timeouts gracefully and tries multiple selectors before failing.  Use `execute_script` to click elements to avoid issues with Selenium's click function. Use incognito mode to avoid detection. Address WebGL deprecation warnings by using the `--enable-unsafe-swiftshader` flag or investigating alternative solutions. Investigate and address "GPU stall due to ReadPixels" errors by optimizing image handling or exploring alternative rendering methods. Added 2025-05-19: Implement a retry mechanism for operations that may fail due to network issues or anti-scraping measures. Implement robust error handling for `InvalidSessionIdException` and other Selenium exceptions. Implement a mechanism to switch to a new tab or window after opening a link in a new tab. Implement a mechanism to save page screenshots and HTML content for debugging purposes. Implement a mechanism to handle cases where the focus is not automatically set to a new tab after opening a link in a new tab. Implement scrolling to the bottom and top of the page before locating elements.  Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab. Implement scrolling to the bottom and top of the page before attempting to locate elements. Added 2025-05-19: Save screenshots of the page for debugging purposes.
- When encountering errors, check the log files for detailed information to help diagnose the problem.
- Ensure necessary imports are included; for example, `from selenium.common.exceptions import TimeoutException`.
- Implement a mechanism to handle potential focus issues after opening a new tab. Implement scrolling to the bottom and top of the page before attempting to locate elements.
- Implement a mechanism to save page screenshots and HTML content for debugging purposes.  Added 2025-05-19: Implement a retry mechanism for operations that may fail due to network issues or anti-scraping measures. Implement robust error handling for `InvalidSessionIdException` and other Selenium exceptions. Implement a mechanism to switch to a new tab or window after opening a link in a new tab.  Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab. Implement scrolling to the bottom and top of the page before attempting to locate elements.
- Implement scrolling to the bottom and top of the page before attempting to locate elements. Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab.
- Implement a mechanism to handle potential focus issues after opening a new tab.  Added 2025-05-19: Implement scrolling to the bottom and top of the page before attempting to locate elements.
- Implement a mechanism to save page screenshots and HTML content for debugging purposes. Added 2025-05-19:  A mechanism should be implemented to handle potential focus issues after opening a new tab.  The script should scroll to the bottom and top of the page before attempting to locate elements.  Added 2025-05-19:  The script should save screenshots of the page for debugging purposes.  Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab.
- If the request status code is not 200, the program should terminate. Added 2025-05-19


## WEB SCRAPING GUIDELINES (Added 2025-05-14)

- When scraping websites, always respect the website's robots.txt file.
- Avoid overloading the website with requests. Implement random delays between requests using a `random_sleep` function.
- Use appropriate headers to mimic a real browser. Use a random user agent from a list of user agents.
- Implement robust error handling to deal with potential issues such as timeouts, network errors, and changes in website structure.
- If a website employs anti-scraping mechanisms, consider using techniques such as rotating proxies, headless browsers, and JavaScript injection to bypass these mechanisms. But always prioritize ethical and legal considerations. Added 2025-05-14: Implement techniques to mask the use of Selenium, such as modifying the `navigator.webdriver` property and using random user agents.  Consider using incognito mode to avoid detection. Added 2025-05-19: Use multiple selectors to locate elements. Implement a retry mechanism for operations that may fail due to network issues or anti-scraping measures.
- Add necessary cookies using `driver.add_cookie(cookie_dict)`, where `cookie_dict` is a dictionary containing at least 'name' and 'value' fields, and optionally 'domain'.  Cookies should be added after the initial page load. Consider storing cookies in a configuration file for easier management.
- Automatically obtain and save cookies after the initial page load to a file named `cookies.json`.
- Implement scrolling to the bottom and top of the page before attempting to locate elements. Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab.
- Before locating elements, the script should scroll to the bottom and top of the page.
- Added 2025-05-19: Implement a mechanism to handle potential focus issues after opening a new tab.
- Added 2025-05-19:  The script should save screenshots of the page for debugging purposes.
- Added 2025-05-19: The script should save the HTML content of the page to `telecom_homepage.html` for debugging purposes.


## EXAMPLE CODE (Added 2025-05-14)

The following code snippets demonstrate best practices for web scraping with Selenium and logging:

**logger_config.py:**

```python
import logging
import os
from datetime import datetime
import glob

class Logger:
    def __init__(self, name='ChinaTelecom', log_dir='logs'):
        self.log_dir = log_dir
        self.name = name
        self.setup_logger()

    def setup_logger(self):
        # Create logs directory if it doesn't exist
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        # Generate log file name with timestamp
        current_date = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.log_dir, f'telecom_{current_date}.log')

        # Create logger instance
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)

        # Check if the same handler has already been added to avoid duplicate logs
        if not any(isinstance(h, logging.FileHandler) and h.baseFilename == os.path.abspath(log_file) for h in self.logger.handlers):
            # Create file handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)

            # Create console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)

            # Create formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            # Set formatter for handlers
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            # Add handlers to logger
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)

        #Clean up old log files
        log_files = sorted(glob.glob(os.path.join(self.log_dir, 'telecom_*.log')))
        if len(log_files) > 5:
            for f in log_files[:-5]:
                os.remove(f)

    def debug(self, message):
        self.logger.debug(message)

    def info(self, message):
        self.logger.info(message)

    def warning(self, message):
        self.logger.warning(message)

    def error(self, message):
        self.logger.error(message)

    def critical(self, message):
        self.logger.critical(message)

logger = Logger(name='ChinaTelecom').logger
```

**test.py:**

```python
import logging
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import time
import json
import random
from logger_config import logger
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests

def setup_driver():
    logger.info("Initializing Chrome driver...")
    chrome_options = Options()
    chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    #chrome_options.add_argument('--incognito')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-popup-blocking')
    chrome_options.add_argument('--disable-notifications')
    chrome_options.add_argument('--disable-infobars')
    chrome_options.add_argument('--start-maximized')
    chrome_options.add_argument('--blink-settings=imagesEnabled=false')
    chrome_options.add_argument('--enable-unsafe-swiftshader') #added to address WebGL deprecation warnings
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
    ]
    chrome_options.add_argument(f'user-agent={random.choice(user_agents)}')
    driver = webdriver.Chrome(options=chrome_options)
    driver.set_page_load_timeout(30)
    driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
        'source': '''
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en']
            });
        '''
    })
    logger.info("Chrome driver initialized")
    return driver

def random_sleep(min_seconds=2, max_seconds=5):
    sleep_time = random.uniform(min_seconds, max_seconds)
    time.sleep(sleep_time)

def wait_for_element(driver, by, value, timeout=20):
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except TimeoutException:
        logger.error(f"Waiting for element timed out: {value}")
        return None

def capture_network_requests(driver, url_prefix):
    logger.info(f"Starting to capture network requests, target URL prefix: {url_prefix}")
    logs = driver.get_log('performance')
    network_requests = []
    for log in logs:
        try:
            log_data = json.loads(log['message'])['message']
            if 'Network.requestWillBeSent' in log_data['method']:
                request_url = log_data['params']['request']['url']
                if request_url.startswith(url_prefix):
                    request_info = {
                        'url': request_url,
                        'method': log_data['params']['request']['method'],
                        'headers': log_data['params']['request']['headers']
                    }
                    network_requests.append(request_info)
                    logger.debug(f"Captured target request: {request_url}")
        except Exception as e:
            logger.error(f"Error processing network request log: {str(e)}")
            continue
    logger.info(f"Network request capture complete, {len(network_requests)} target requests captured")
    return network_requests

def main():
    logger.info("Starting automated task")
    driver = setup_driver()
    try:
        logger.info("Opening China Telecom website...")
        driver.get('https://www.189.cn/')
        random_sleep(2, 4)
        
        # ====== 自动获取并保存 cookie ======
        cookies = driver.get_cookies()
        with open('cookies.json', 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        logger.info("已将 cookie 保存到 cookies.json")
        # ====== 自动获取并保存 cookie 结束 ======


        menu = None
        menu_selectors = [
            (By.CLASS_NAME, 'menu.z222'),
            (By.CLASS_NAME, 'menu'),
            (By.XPATH, "//div[contains(@class, 'menu')]"),
            (By.XPATH, "//nav[contains(@class, 'menu')]"),
            (By.XPATH, "//div[contains(@class, 'nav')]"),
            (By.XPATH, "//ul[contains(@class, 'nav')]")
        ]
        for by, selector in menu_selectors:
            logger.info(f"Trying to locate menu element: {selector}")
            menu = wait_for_element(driver, by, selector)
            if menu:
                logger.info(f"Successfully found menu element: {selector}")
                break
        if not menu:
            raise Exception("Could not find menu element")
        random_sleep()
        zifei_button = None
        button_selectors = [
            (By.XPATH, "//a[contains(text(), '资费专区')]"),
            (By.XPATH, "//a[contains(., '资费专区')]"),
            (By.XPATH, "//*[contains(text(), '资费专区')]"),
            (By.LINK_TEXT, "资费专区"),
            (By.XPATH, "//a[contains(@href, 'zifei')]"),
            (By.XPATH, "//a[contains(@href, 'fee')]")
        ]
        for by, selector in button_selectors:
            try:
                logger.info(f"Trying to locate 资费专区 button: {selector}")
                zifei_button = driver.find_element(by, selector)
                if zifei_button:
                    logger.info(f"Successfully found 资费专区 button: {selector}")
                    break
            except NoSuchElementException:
                continue
        if not zifei_button:
            raise Exception("Could not find 资费专区 button")
        random_sleep()
        logger.info("Clicking 资费专区 button...")
        driver.execute_script("arguments[0].click();", zifei_button)
        logger.info("Clicked 资费专区 button")
        random_sleep(4, 7)
        target_url_prefix = 'https://gd.189.cn/J/J10390.j'
        network_requests = capture_network_requests(driver, target_url_prefix)
        for request in network_requests:
            logger.info("\nCaptured request information:")
            logger.info(f"URL: {request['url']}")
            logger.info(f"Method: {request['method']}")
            logger.info("Headers:")
            for key, value in request['headers'].items():
                logger.info(f"  {key}: {value}")
    except Exception as e:
        logger.error(f"Error during execution: {str(e)}", exc_info=True)
    finally:
        logger.info("Closing browser...")
        driver.quit()
        logger.info("Browser closed")
        logger.info("Automated task complete")

if __name__ == "__main__":
    main()
```