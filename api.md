# 运营商资费数据采集 API 文档

## 概述

本 API 提供统一的接口来采集各运营商（移动、电信、联通、广电）的资费数据。API 采用异步处理方式，支持回调通知，并返回压缩后的数据文件。

## 基础信息

- 基础URL: `http://your-domain:8123`
- 所有请求和响应均使用 JSON 格式
- 所有时间戳格式为 `YYYYMMDD_HHMMSS`

## 接口列表

### 1. 启动爬虫任务

启动一个数据采集任务，支持指定运营商类型和省份。

#### 请求信息

- **URL**: `/api/v1/crawl`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数

```json
{
    "provinces": "011",           // 省份编码，字符串类型
    "callback_url": "http://example.com/callback",  // 回调URL
    "crawler_type": "mobile"      // 爬虫类型
}
```

参数说明：
- `provinces`: 省份编码，字符串类型，例如 "011" 表示北京
- `callback_url`: 回调通知的URL地址，必须是一个有效的HTTP(S) URL
- `crawler_type`: 爬虫类型，可选值：
  - `mobile`: 中国移动
  - `telecom`: 中国电信
  - `unicom`: 中国联通
  - `broadnet`: 中国广电

#### 响应信息

```json
{
    "task_id": "mobile_20240315123456",  // 任务ID
    "status": "accepted",                // 任务状态
    "message": "任务已接受"              // 状态描述
}
```

### 2. 查询任务状态

查询指定任务ID的执行状态。

#### 请求信息

- **URL**: `/api/v1/task/{task_id}`
- **方法**: GET

#### 路径参数

- `task_id`: 任务ID，格式为 `{crawler_type}_{timestamp}`

#### 响应信息

```json
{
    "status": "completed",           // 任务状态
    "message": "任务完成",           // 状态描述
    "zip_file": "/path/to/file.zip"  // 生成的文件路径（仅当任务完成时返回）
}
```

任务状态说明：
- `running`: 任务正在执行
- `completed`: 任务已完成
- `failed`: 任务执行失败

## 回调通知

当任务完成时，系统会向指定的回调URL发送POST请求，请求体为multipart/form-data格式，包含以下字段：

- `file`: 压缩后的数据文件（application/zip）
- `filename`: 文件名
- `timestamp`: 时间戳
- `crawlerUrl`: 数据源URL，格式如下：
  - 移动：`https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov={province}`
  - 联通：`https://img.client.10010.com/zifeizhuanquwt/index.html?time=1747278766317#/home?procode={province}`
  - 电信：`https://www.189.cn/jtzfzq`
  - 广电：`https://m.10099.com.cn/expensesNotice/#/home?codeValue=...`

## 错误处理

当发生错误时，API 将返回相应的 HTTP 状态码和错误信息：

```json
{
    "detail": "错误描述信息"
}
```

常见错误码：
- 400: 请求参数错误
- 404: 任务不存在
- 500: 服务器内部错误

## 示例

### 启动移动数据采集任务

```bash
curl -X POST "http://your-domain:8000/api/v1/crawl" \
     -H "Content-Type: application/json" \
     -d '{
         "provinces": "011",
         "callback_url": "http://example.com/callback",
         "crawler_type": "mobile"
     }'
```

### 查询任务状态

```bash
curl "http://your-domain:8000/api/v1/task/mobile_20240315123456"
```

## 注意事项

1. 回调URL必须是一个有效的HTTP(S)地址
2. 任务执行可能需要较长时间，建议使用异步处理方式
3. 生成的数据文件会在服务器上保留一段时间，建议及时下载
4. 建议在回调接口中实现幂等性处理，避免重复处理同一任务 