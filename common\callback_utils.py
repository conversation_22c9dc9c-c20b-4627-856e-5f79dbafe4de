import os
import logging
import requests
import aiohttp
from typing import Dict, Any, Optional, Union

# 配置日志
logger = logging.getLogger(__name__)

def _check_file_status(file_path: Optional[str], status: str = "200") -> tuple:
    """检查文件状态，包括是否存在和大小是否为0
    
    Args:
        file_path: 文件路径，可能为None
        status: 当前状态码，默认为"200"
        
    Returns:
        tuple: (新状态码, 是否有效文件, 文件大小或错误消息)
    """
    # 如果没有提供文件路径
    if not file_path:
        return status, False, "未提供文件路径"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        # 如果文件不存在但状态码是200，改为500
        new_status = "500" if status == "200" else status
        return new_status, False, f"文件不存在: {file_path}"
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        # 如果文件大小为0但状态码是200，改为500
        new_status = "500" if status == "200" else status
        return new_status, False, f"文件大小为0: {file_path}"
    
    # 文件存在且大小大于0
    logger.info(f"文件大小: {file_size} 字节")
    return status, True, file_size

def sync_callback(
    callback_url: Optional[str], 
    file_path: Optional[str], 
    crawler_url: str, 
    status: str = "200"
) -> Dict[str, Any]:
    """同步回调函数，将zip文件通过FormData发送到指定的URL
    
    Args:
        callback_url: 回调URL地址，如果为None则不执行回调
        file_path: zip文件路径，如果为None或不存在则只发送状态
        crawler_url: 爬虫URL地址
        status: 状态码，默认为"200"，失败时为"500"
        
    Returns:
        Dict[str, Any]: 回调响应的JSON数据或错误信息
    """
    # 如果没有提供回调URL，直接返回
    if not callback_url:
        logger.info("未提供回调URL，跳过回调")
        return {"status": "skipped", "message": "未提供回调URL"}
    
    try:
        # 检查文件状态
        status, is_valid_file, file_info = _check_file_status(file_path, status)
        
        # 准备表单数据
        data = {
            'crawlerUrl': crawler_url,
            'status': status
        }
        
        files = {}
        
        # 如果文件有效，准备文件数据
        if is_valid_file:
            files = {
                'file': (os.path.basename(file_path), open(file_path, 'rb'), 'application/octet-stream')
            }
        else:
            # 记录文件状态信息
            logger.warning(file_info)
        
        try:
            # 发送请求
            response = requests.post(
                callback_url,
                files=files,
                data=data
            )
            
            # 检查响应
            if response.status_code == 200:
                logger.info(f"回调成功，状态: {status}")
                try:
                    return response.json()
                except:
                    return {"status": status, "message": response.text}
            else:
                logger.error(f"回调失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return {"status": status, "message": f"回调失败，状态码: {response.status_code}, 响应: {response.text}"}
                
        finally:
            # 确保文件被关闭
            if files and 'file' in files:
                files['file'][1].close()
                
    except Exception as e:
        logger.error(f"发送回调时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}

async def async_callback(
    callback_url: Optional[str], 
    file_path: Optional[str], 
    crawler_url: str, 
    status: str = "200"
) -> Dict[str, Any]:
    """异步回调函数，将zip文件通过FormData发送到指定的URL
    
    Args:
        callback_url: 回调URL地址，如果为None则不执行回调
        file_path: zip文件路径，如果为None或不存在则只发送状态
        crawler_url: 爬虫URL地址
        status: 状态码，默认为"200"，失败时为"500"
        
    Returns:
        Dict[str, Any]: 回调响应的JSON数据或错误信息
    """
    # 如果没有提供回调URL，直接返回
    if not callback_url:
        logger.info("未提供回调URL，跳过回调")
        return {"status": "skipped", "message": "未提供回调URL"}
    
    try:
        # 检查文件状态
        status, is_valid_file, file_info = _check_file_status(file_path, status)
        
        # 准备数据
        file_to_close = None
        
        async with aiohttp.ClientSession() as session:
            # 准备回调数据
            data = aiohttp.FormData()
            
            # 添加状态码
            data.add_field('status', status)
            
            # 添加crawlerUrl字段
            data.add_field('crawlerUrl', crawler_url)
            
            # 如果文件有效，添加文件
            if is_valid_file:
                file_to_close = open(file_path, 'rb')
                data.add_field('file',
                             file_to_close,
                             filename=os.path.basename(file_path))
            else:
                # 记录文件状态信息
                logger.warning(file_info)
            
            try:
                # 发送回调
                async with session.post(callback_url, data=data) as response:
                    response_text = await response.text()
                    logger.info(f"收到响应状态码: {response.status}")
                    
                    if response.status != 200:
                        logger.error(f"回调失败: {response.status} - {response_text}")
                        return {"status": status, "message": f"回调失败: {response_text}"}
                    
                    try:
                        return await response.json()
                    except:
                        return {"status": status, "message": response_text}
            finally:
                # 确保文件被关闭
                if file_to_close:
                    file_to_close.close()
                    
    except Exception as e:
        logger.error(f"发送回调时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}

def handle_callback(
    callback_url: Optional[str], 
    file_path: Optional[str], 
    crawler_url: str, 
    status: str = "200",
    is_async: bool = False
) -> Union[Dict[str, Any], None]:
    """处理回调，根据是否异步选择合适的回调方法
    
    Args:
        callback_url: 回调URL地址，如果为None则不执行回调
        file_path: zip文件路径，如果为None或不存在则只发送状态
        crawler_url: 爬虫URL地址
        status: 状态码，默认为"200"，失败时为"500"
        is_async: 是否使用异步回调，默认为False
        
    Returns:
        Union[Dict[str, Any], None]: 同步模式下返回回调结果，异步模式下返回None
    """
    if not callback_url:
        return None
        
    try:
        if is_async:
            # 异步模式下，返回协程对象
            return async_callback(callback_url, file_path, crawler_url, status)
        else:
            # 同步模式下，直接执行并返回结果
            return sync_callback(callback_url, file_path, crawler_url, status)
    except Exception as e:
        logger.error(f"处理回调时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}



