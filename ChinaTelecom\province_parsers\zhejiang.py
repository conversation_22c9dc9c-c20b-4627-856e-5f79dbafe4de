from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_zhejiang_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析浙江资费页面内容（适配新结构，支持瀑布流加载）
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析浙江{menu_type}的页面内容...")
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.content-list .newZifei", timeout=10000)
        # 最大化窗口
        logger.info("最大化浏览器窗口...")
        await page.evaluate("""() => {window.moveTo(0, 0);window.resizeTo(screen.availWidth, screen.availHeight);}""")
        await page.wait_for_timeout(2000)
        # 暴力PageDown滚动方案
        logger.info("开始暴力PageDown滚动...")
        max_retries = 200
        retry_count = 0
        last_count = 0
        same_count_times = 0
        while retry_count < max_retries:
            cards = await page.query_selector_all("div.newZifei > div.model")
            current_count = len(cards)
            logger.info(f"当前加载产品数量: {current_count}")
            if current_count == last_count:
                same_count_times += 1
                if same_count_times >= 3:
                    logger.info("产品数量连续3次未变化，认为加载完成")
                    break
            else:
                same_count_times = 0
                last_count = current_count
            # 暴力PageDown滚动，每次点击100次
            for _ in range(100):
                await page.keyboard.press("PageDown")
            await page.wait_for_timeout(1000)
            # 查找查看更多按钮（如有）
            load_more_button = await page.query_selector(".common-more-btn")
            if load_more_button:
                await load_more_button.wait_for_element_state('visible')
                logger.info("点击查看更多按钮...")
                await page.evaluate("""(button) => {
                    const event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    button.dispatchEvent(event);
                }""", load_more_button)
                logger.info("点击查看更多按钮")
                await page.wait_for_timeout(2000)
            retry_count += 1
        logger.info("内容加载完成")
        # 解析所有产品卡片
        products = await page.query_selector_all("div.newZifei > div.model")
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        logger.info(f"找到 {len(products)} 个产品项")
        products_data = []
        for index, product in enumerate(products, 1):
            try:
                # 标题
                title_elem = await product.query_selector("..//div[@class='content-title']//span[@tagname='pcTitle']")
                code_elem = await product.query_selector("..//div[@class='content-title']//span[@tagname='contentCode']")
                title = await title_elem.text_content() if title_elem else ""
                code = await code_elem.text_content() if code_elem else ""
                # 基本信息
                basic_info = {}
                items = await product.query_selector_all(".jbxx-content > .item")
                for item in items:
                    key_elem = await item.query_selector(".item-title")
                    value_elem = await item.query_selector("div[tagname], span[tagname], div:not([class])")
                    key = await key_elem.text_content() if key_elem else ""
                    value = await value_elem.text_content() if value_elem else ""
                    if key:
                        basic_info[key.strip().replace('：','')] = value.strip()
                # 服务内容
                service_content = {}
                table = await product.query_selector("table")
                if table:
                    thead_cells = await table.query_selector_all("thead tr.thead td")
                    tbody_cells = await table.query_selector_all("tbody tr td")
                    if thead_cells and tbody_cells and len(thead_cells) == len(tbody_cells):
                        for th, td in zip(thead_cells, tbody_cells):
                            th_text = await th.text_content()
                            td_text = await td.text_content()
                            service_content[th_text.strip()] = td_text.strip()
                # 备注/超出资费/其他说明
                extra_fees = other_content = others = ""
                zifei_detail = await product.query_selector(".zifei_detail_pc")
                if zifei_detail:
                    # 超出资费
                    extra_fees_elem = await zifei_detail.query_selector("div[tagparent='extra_fees'] div[tagname='extra_fees']")
                    if extra_fees_elem:
                        extra_fees = await extra_fees_elem.text_content()
                    # 其他服务内容
                    other_content_elem = await zifei_detail.query_selector("div[tagparent='other_content'] div[tagname='other_content']")
                    if other_content_elem:
                        other_content = await other_content_elem.text_content()
                    # 其他说明
                    others_elem = await zifei_detail.query_selector("div[tagparent='others'] div[tagname='others']")
                    if others_elem:
                        others = await others_elem.text_content()
                # 组装数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "extra_fees": extra_fees.strip(),
                    "other_content": other_content.strip(),
                    "others": others.strip()
                }
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
            except Exception as e:
                logger.error(f"解析第 {index} 个产品时发生错误: {str(e)}")
                continue
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        if products_data:
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_zhejiang_menu_config() -> dict:
    """获取浙江的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.demo-tabs",
        'menu_selector': "div.demo-tabs > div.mulu1-tab",
        'list_selector': "div.newZifei > div.model"
    } 