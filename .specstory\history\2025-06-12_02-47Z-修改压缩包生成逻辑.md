<!-- Generated by SpecStory -->

# 修改压缩包生成逻辑 (2025-06-12 02:47Z)

_**User**_

发生错误，不要生成压缩包

---

_**Assistant**_

你希望在发生错误时，不要生成压缩包（即不执行压缩 JSON 文件的操作）。  
我会为你说明修改思路，并准备好具体的修改方案：

### 你的需求
- 只在数据采集全部成功时才压缩生成的 JSON 文件。
- 如果采集过程中发生异常（包括重试失败），则不生成压缩包。

### 现有逻辑分析
在 `UnicomScraper.run()` 方法中，压缩包的生成是在所有采集流程（全国资费、本省资费）都成功后执行的：
```python
self._compress_json_files()
```
但如果采集失败，异常被捕获后，依然会进入 `finally`，但不会再压缩。

**问题点**：  
如果部分流程成功，部分失败，或者重试后依然失败，`self._compress_json_files()` 可能还是会被调用。

### 修改建议
- 只在所有采集流程都成功时才调用 `self._compress_json_files()`。
- 在异常和重试失败的情况下，**不要**调用压缩方法。

### 具体实现
1. 在 `run()` 方法中，设置一个标志变量 `all_success = False`。
2. 只有在全国资费和本省资费都采集成功时，才设置 `all_success = True`。
3. 只有 `all_success` 时才调用 `self._compress_json_files()`。

我将为你实现这一修改。

---

Read file: ChinaUnicom/unicom_crawler.py

---

Read file: ChinaUnicom/unicom_crawler.py

---

我将对 `run` 方法进行如下修改：

- 增加 `all_success = False` 标志。
- 只有全国资费和本省资费都采集成功时，才设置 `all_success = True`。
- 只有 `all_success` 时才调用 `self._compress_json_files()`，否则不生成压缩包。

这样可以确保发生错误时不会生成压缩包。

我将为你实现这一修改。

---

```diff
          retry_count = 0
          last_error = None
+         all_success = False  # 标志：只有全部成功才压缩
          
          while retry_count < max_retries:
```

```diff
                          logger.info("本省资费数据采集完成")
                          self._save_responses("本省资费")
+                         all_success = True  # 只有全部成功才置为True
                      else:
                          logger.error("本省资费数据采集失败")
```

```diff
                      raise Exception("未找到本省资费标签")
  
-                 # 数据采集完成后压缩文件
-                 self._compress_json_files()
-                 logger.info("数据采集完成")
+                 # 只有全部成功才压缩文件
+                 if all_success:
+                     self._compress_json_files()
+                     logger.info("数据采集完成")
+                 else:
+                     logger.info("采集未全部成功，不生成压缩包")
                  
                  # 如果执行到这里没有异常，说明成功完成，跳出重试循环
```

---

已完成修改：  
- 只有全国资费和本省资费都采集成功时才会生成压缩包。  
- 发生任何错误或部分流程失败时，不会生成压缩包，只会保存 json 数据。

你可以直接运行脚本验证效果。如果还有其他采集或异常处理需求，欢迎随时告知！

---

_**User**_

重新执行不要将之前的文件添加到zip中

---

_**Assistant**_

你的需求是：**重新执行（重试）时，不要把之前采集失败时生成的 JSON 文件添加到 zip 压缩包中**，只压缩本次成功采集的数据。

### 问题分析
目前 `self.generated_files` 只在 `_compress_json_files` 后被清空并添加 zip 路径，但在重试时，之前采集失败时生成的 JSON 文件路径可能还在 `self.generated_files` 里，导致多次重试后，旧的 JSON 文件也被加入 zip 包。

### 解决方案
1. **每次重试前，清空 `self.generated_files`**，确保只保存本次采集的数据文件路径。
2. 这样，只有本次 run 成功采集的数据会被压缩。

### 实现方法
在 `run()` 方法的 while 循环开始处（每次重试前），加一句：
```python
self.generated_files = []
```

这样可以保证每次重试都是“干净”的，不会把之前失败时的文件带入 zip 包。

我将为你实现这一修改。

---

```diff
          
          while retry_count < max_retries:
+             # 每次重试前清空已生成文件列表，避免旧文件被加入zip
+             self.generated_files = []
              try:
                  if retry_count > 0:
```

---

已完成修改：  
每次重试前都会清空 `self.generated_files`，确保只有本次采集成功的数据会被压缩进 zip 包，之前失败时生成的文件不会被加入。

你可以直接运行脚本验证效果。如果还有其他采集、文件管理或异常处理需求，欢迎随时告知！

---

