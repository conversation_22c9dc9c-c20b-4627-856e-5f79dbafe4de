2025-07-25 12:08:21 - MainProcess - 59312 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250725
2025-07-25 12:08:21 - MainProcess - 59312 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-25 12:08:21 - MainProcess - 59312 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-25 12:08:22 - MainProcess - 59312 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-25 12:10:22 - MainProcess - 45516 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250725
2025-07-25 12:10:23 - MainProcess - 45516 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-25 12:10:23 - MainProcess - 45516 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-25 12:10:23 - MainProcess - 45516 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-25 12:10:28 - MainProcess - 45516 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\ah_telecom_7548_资费数据.mp4
2025-07-25 12:11:26 - MainProcess - 36760 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250725
2025-07-25 12:11:27 - MainProcess - 36760 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-25 12:11:27 - MainProcess - 36760 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-25 12:11:27 - MainProcess - 36760 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-25 12:12:33 - MainProcess - 33776 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250725
2025-07-25 12:12:33 - MainProcess - 33776 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-25 12:12:33 - MainProcess - 33776 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-25 12:12:33 - MainProcess - 33776 - common - INFO - [screen_record.py:53] - 开始录屏...
