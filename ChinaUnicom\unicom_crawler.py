import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import sys
import zipfile
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import psutil
import threading
import time
import random
import json
import requests
from selenium.webdriver.common.action_chains import ActionChains
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
from common.logger_config import unicom_logger as logger
# 导入窗口操作工具
from common.window_utils import split_window_to_right_half, move_window_to_bottom_right, get_adaptive_window_size, activate_window
# 导入公共回调方法
from common.callback_utils import sync_callback
from common.screen_recorder import ScreenRecorder
# 导入HTML保存工具
from common.html_saver import html_saver
# 导入配置加载工具
from common.config_loader import is_screen_recording_enabled

@dataclass
class ScraperConfig:
    """配置类，存储爬虫的配置信息"""
    target_urls: List[str] = None
    main_url: str = None
    data_dir: str = None  # 基础数据目录
    zip_dir: str = None   # 基础压缩目录
    log_dir: str = None   # 基础日志目录
    data_date_dir: str = None  # 日期数据目录
    zip_date_dir: str = None   # 日期压缩目录
    log_date_dir: str = None   # 日期日志目录
    provinces: List[str] = None  # 添加省份参数
    provic_dict: dict = None

    def __post_init__(self):
        if self.target_urls is None:
            self.target_urls = [
                "https://m.client.10010.com/servicequerybusiness/queryTariff/tariffDetailInfoChange",
                "https://m.client.10010.com/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange"
            ]
        if self.main_url is None:
            # 根据省份参数构建URL
            self.province_code = self.provinces[0] if self.provinces and len(self.provinces) > 0 else None  # 默认使用011
            logger.info(f"使用省份代码: {self.province_code}")
            self.main_url = f"https://img.client.10010.com/zifeizhuanquwt/index.html?time=1747278766317#/home?procode={self.province_code}"

class UnicomScraper:
    """联通资费爬虫类"""
    
    def __init__(self, config: Optional[ScraperConfig] = None):
        """初始化爬虫
        
        Args:
            config: 爬虫配置，如果为None则使用默认配置
        """
        self.config = config or ScraperConfig()
        self.driver = None
        self.wait = None
        self.captured_responses: Dict[str, Any] = {}
        self.generated_files: List[str] = []
        self.first_option_texts: Dict[int, str] = {}
        self.second_option_texts: Dict[int, str] = {}
        self.province_code = self.config.province_code  # 从config中获取province_code
        self.provic_dict = {
            "011": "北京",
            "030": "安徽",
            "083": "重庆",
            "038": "福建",
            "087": "甘肃",
            "051": "广东",
            "059": "广西",
            "085": "贵州",
            "050": "海南",
            "018": "河北",
            "076": "河南",
            "097": "黑龙江",
            "071": "湖北",
            "074": "湖南",
            "090": "吉林",
            "034": "江苏",
            "075": "江西",
            "091": "辽宁",
            "010": "内蒙古",
            "088": "宁夏",
            "070": "青海",
            "017": "山东",
            "019": "山西",
            "084": "陕西",
            "031": "上海",
            "081": "四川",
            "013": "天津",
            "079": "西藏",
            "089": "新疆",
            "086": "云南",
            "036": "浙江"
        }
        self.province_name = self.provic_dict[self.province_code]
        
        # 确保目录存在
        self._ensure_directories()
        logger.info(f"省份代码: {self.province_code}")
    def _ensure_directories(self) -> None:
        """确保数据目录、zip目录和logs目录存在，并创建日期子文件夹"""
        try:
            # 获取当前文件所在目录的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 获取当前日期
            current_date = time.strftime("%Y%m%d")
            
            # 使用 os.path.join 来正确处理路径分隔符
            base_dirs = {
                'data_dir': os.path.join(current_dir, "data"),
                'zip_dir': os.path.join(current_dir, "zip"),
                'log_dir': os.path.join(current_dir, "logs")
            }
            
            # 创建基础目录和日期子目录
            for dir_type, base_dir in base_dirs.items():
                # 创建基础目录
                if not os.path.exists(base_dir):
                    os.makedirs(base_dir, exist_ok=True)
                    logger.info(f"创建基础目录: {base_dir}")
                
                # 创建日期子目录
                date_dir = os.path.join(base_dir, current_date)
                if not os.path.exists(date_dir):
                    os.makedirs(date_dir, exist_ok=True)
                    logger.info(f"创建日期子目录: {date_dir}")
                
                # 更新配置中的目录路径
                setattr(self.config, dir_type, base_dir)
                setattr(self.config, f"{dir_type.split('_')[0]}_date_dir", date_dir)
            
            logger.info(f"数据目录: {self.config.data_dir}")
            logger.info(f"数据日期目录: {self.config.data_date_dir}")
            logger.info(f"压缩目录: {self.config.zip_dir}")
            logger.info(f"压缩日期目录: {self.config.zip_date_dir}")
            logger.info(f"日志目录: {self.config.log_dir}")
            logger.info(f"日志日期目录: {self.config.log_date_dir}")
            
        except Exception as e:
            logger.error(f"创建目录结构时出错: {str(e)}")
            raise

    def setup_driver(self) -> None:
        """初始化Chrome浏览器驱动"""
        logger.info("正在初始化Chrome驱动...")
        chrome_options = Options()
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-popup-blocking')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--start-maximized')
        chrome_options.add_argument('--blink-settings=imagesEnabled=false')
        chrome_options.add_argument('--enable-unsafe-swiftshader')
        
        # 随机选择用户代理
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        ]
        chrome_options.add_argument(f'user-agent={random.choice(user_agents)}')
        
        # 增加页面加载超时时间
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_page_load_timeout(60)  # 增加到60秒
        self.wait = WebDriverWait(self.driver, 60)  # 初始化WebDriverWait
        
        try:
            # 注入JavaScript来修改navigator属性
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en']
                    });
                '''
            })
            
            logger.info("Chrome驱动初始化完成")
        except Exception as e:
            logger.error(f"初始化驱动时发生错误: {str(e)}")
            raise

    def _random_sleep(self, min_seconds: float = 1, max_seconds: float = 3) -> None:
        """随机等待一段时间
        
        Args:
            min_seconds: 最小等待时间
            max_seconds: 最大等待时间
        """
        sleep_time = random.uniform(min_seconds, max_seconds)
        time.sleep(sleep_time)

    def _scroll_to_bottom(self) -> None:
        """滚动到页面底部并等待内容加载"""
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_attempts = 3
        
        while True:
            # 先向上滚动一小段距离来触发加载机制
            current_position = self.driver.execute_script("return window.pageYOffset;")
            self.driver.execute_script(f"window.scrollTo(0, {max(0, current_position - 100)});")
            self._random_sleep(1, 2)
            
            # 再向下滚动到底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self._random_sleep(2, 2)            
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            
            if new_height == last_height:
                scroll_attempts += 1
                if scroll_attempts >= max_attempts:
                    logger.info("多次尝试后确认没有更多内容可加载")
                    break
            else:
                scroll_attempts = 0
            last_height = new_height
            logger.info("已滚动到新内容")

    def _scroll_to_top(self) -> None:
        """滚动到页面顶部"""
        try:
            self.driver.execute_script("window.scrollTo(0, 0);")
            self._random_sleep(1, 2)
            logger.info("已滚动到页面顶部")
        except Exception as e:
            logger.error(f"滚动到顶部时发生错误: {str(e)}")

    def _capture_network_requests(self) -> List[Dict[str, str]]:
        """捕获目标URL的网络请求
        
        Returns:
            捕获到的请求列表
        """
        logs = self.driver.get_log('performance')
        requests = []
        
        for log in logs:
            try:
                log_data = json.loads(log['message'])['message']
                if 'Network.responseReceived' in log_data['method']:
                    if 'params' in log_data and 'response' in log_data['params']:
                        response = log_data['params']['response']
                        response_url = response.get('url', '')
                        if any(target_url in response_url for target_url in self.config.target_urls):
                            request_info = {
                                'url': response_url,
                                'requestId': log_data['params'].get('requestId', '')
                            }
                            requests.append(request_info)
                            logger.info(f"捕获到目标响应: {response_url}")
            except Exception as e:
                logger.error(f"处理网络日志时发生错误: {str(e)}")
                continue
        
        return requests

    def _process_request(self, request: Dict[str, str]) -> None:
        """处理单个网络请求
        
        Args:
            request: 请求信息
        """
        try:
            if 'requestId' not in request:
                return
                
            resp = self.driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request['requestId']})
            if 'body' not in resp:
                return
                
            # 根据URL确定数据类型
            tariff_type = "tariff_menu_data" if "TariffMenuDataDetailHomePageChange" in request['url'] else "national_tariff"
            self._process_response(resp['body'], tariff_type)
            
        except WebDriverException as e:
            logger.error(f"获取响应数据失败: {str(e)}")

    def _process_response(self, response_body: str, tariff_type: str) -> None:
        """处理响应内容
        
        Args:
            response_body: 响应内容
            tariff_type: 资费类型
        """
        try:
            response_data = json.loads(response_body)
            self.captured_responses[tariff_type] = response_data
            logger.info(f"已保存接口响应数据: {json.dumps(response_data, ensure_ascii=False)}")
        except json.JSONDecodeError:
            self.captured_responses[tariff_type] = response_body
            logger.info("已保存原始响应数据")

    def _find_and_click_local_tariff(self) -> bool:
        """查找并点击本省资费标签
        
        Returns:
            是否成功找到并点击
        """
        try:
            logger.info("等待资费标签加载...")
            group_elements = self.wait.until(
                EC.presence_of_all_elements_located((By.CLASS_NAME, "group"))
            )
            
            for group in group_elements:
                try:
                    p_elements = group.find_elements(By.TAG_NAME, "p")
                    for p in p_elements:
                        if "全国资费" not in p.text:
                            # 点击打开级联选择器
                            ActionChains(self.driver).double_click(p).perform()
                            self._random_sleep(2, 2)
                            
                            # 等待级联菜单出现并完全加载
                            menu = self.wait.until(
                                EC.presence_of_element_located((By.CLASS_NAME, "el-cascader-menu__list"))
                            )
                            
                            # 等待一段时间确保菜单完全加载
                            self._random_sleep(1, 2)
                            
                            # 获取所有省份选项
                            province_options = menu.find_elements(By.CLASS_NAME, "el-cascader-node")
                            
                            # 获取当前省份的中文名称
                            target_province = self.provic_dict.get(self.province_code)
                            if not target_province:
                                logger.error(f"未找到省份代码 {self.province_code} 对应的中文名称")
                                return False
                                
                            logger.info(f"正在查找省份: {target_province}")
                            
                            # 遍历选项找到目标省份
                            for option in province_options:
                                try:
                                    # 获取选项的完整HTML
                                    option_html = option.get_attribute('outerHTML')
                                    logger.info(f"选项HTML: {option_html}")
                                    
                                    # 使用JavaScript获取文本内容
                                    option_text = self.driver.execute_script("""
                                        var element = arguments[0];
                                        var span = element.querySelector('span[data-v-04e83e3d]');
                                        return span ? span.textContent : '';
                                    """, option)
                                    
                                    # logger.info(f"当前选项: {option_text}")
                                    
                                    if option_text == target_province:
                                        # 找到目标省份，点击它
                                        ActionChains(self.driver).move_to_element(option).perform()
                                        logger.info(f"已点击省份: {target_province}")
                                        self._random_sleep(2, 2)
                                        # 省份点击成功后，遍历地市菜单
                                        try:
                                            self.wait.until(lambda d: len(d.find_elements(By.CSS_SELECTOR, "ul.el-cascader-menu__list")) >= 2)
                                            menus = self.driver.find_elements(By.CSS_SELECTOR, "ul.el-cascader-menu__list")
                                            city_menu = menus[1]  # 第二个ul为地市菜单
                                            city_count = len(city_menu.find_elements(By.CSS_SELECTOR, "li.el-cascader-node"))
                                            logger.info(f"检测到 {city_count} 个地市选项，开始遍历...")
                                            for idx in range(city_count):
                                                try:
                                                    # 每次都重新获取city_menu和city_options，保证引用有效
                                                    menus = self.driver.find_elements(By.CSS_SELECTOR, "ul.el-cascader-menu__list")
                                                    city_menu = menus[1]
                                                    city_options = city_menu.find_elements(By.CSS_SELECTOR, "li.el-cascader-node")
                                                    city_option = city_options[idx]
                                                    self.driver.execute_script("arguments[0].click();", city_option)
                                                    self._random_sleep(1, 2)
                                                    try:
                                                        input_elem = self.wait.until(
                                                            lambda d: d.find_element(By.CSS_SELECTOR, 'input.el-input__inner[readonly][placeholder]')
                                                        )
                                                        placeholder = input_elem.get_attribute("placeholder")
                                                        city_name = placeholder.replace("资费", "").strip()
                                                        self.province_name = city_name
                                                        logger.info(f"当前页面input的placeholder: {placeholder}，提取地市名: {city_name}")
                                                    except Exception as e:
                                                        logger.warning(f"获取placeholder时发生错误: {str(e)}")
                                                    # 可在此处插入后续采集逻辑
                                                    if self._select_and_scrape_all_combinations():
                                                        logger.info("本省资费数据采集完成")
                                                        self._save_responses("本省资费")
                                                    else:
                                                        logger.error("本省资费数据采集失败")
                                                        raise Exception("本省资费数据采集失败")
                                                except Exception as e:
                                                    logger.warning(f"点击地市 {idx} 时发生错误: {str(e)}")
                                                    continue
                                            self.all_success = True
                                        except Exception as e:
                                            logger.error(f"遍历地市菜单时发生错误: {str(e)}")
                                        return True
                                except Exception as e:
                                    logger.warning(f"处理选项时发生错误: {str(e)}")
                                    continue
                            
                            logger.error(f"未找到省份: {target_province}")
                            return False
                            
                except Exception as e:
                    logger.warning(f"处理group元素时发生错误: {str(e)}")
                    continue
            
            logger.error("未找到本省资费标签")
            return False
                
        except Exception as e:
            logger.error(f"点击本省资费标签时发生错误: {str(e)}")
            return False

    def _save_responses(self, category: str = 'error') -> None:
        """保存捕获的响应到JSON文件
        
        Args:
            category: 数据类别，用于文件名
        """
        try:
            # 确保目录存在
            self._ensure_directories()
            
            timestamp = time.strftime("%H%M%S")
            json_filename = os.path.join(self.config.data_date_dir, f'{self.province_name}_unicom_tariff_data_{timestamp}_{category}.json')
            
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(self.captured_responses, f, ensure_ascii=False, indent=2)
            logger.info(f"已将捕获的响应保存到 {json_filename}")
            
            self.generated_files.append(json_filename)
                
        except Exception as e:
            logger.error(f"保存响应数据失败: {str(e)}")
            raise

    def _compress_json_files(self) -> None:
        """压缩本次生成的两个JSON文件"""
        try:
            if len(self.generated_files) != 0:
                timestamp = time.strftime("%H%M%S")
                zip_filename = os.path.join(self.config.zip_date_dir, f'{self.province_name}_unicom_tariff_data_{timestamp}.zip')
                
                with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for json_file in self.generated_files:
                        if os.path.exists(json_file):
                            zipf.write(json_file, os.path.basename(json_file))
                            os.remove(json_file)
                logger.info(f"已将JSON文件压缩到 {zip_filename}")
                # 清空generated_files列表并添加zip文件路径
                self.generated_files = [zip_filename]
                logger.info(f"已更新generated_files列表，当前包含: {self.generated_files}")
            else:
                logger.warning(f"未找到足够的JSON文件进行压缩，找到 {len(self.generated_files)} 个文件")
                
        except Exception as e:
            logger.error(f"压缩JSON文件失败: {str(e)}")

    def _select_and_scrape_all_combinations(self) -> bool:
        """遍历所有下拉框组合并采集数据
        
        Returns:
            是否成功完成数据采集
        """
        try:
            logger.info("等待级联下拉框加载...")
            
            # 检查页面是否完全加载
            # try:
            #     self.wait.until(
            #         EC.presence_of_element_located((By.CLASS_NAME, "shujvqu"))
            #     )
            #     logger.info("页面主体已加载")
            # except TimeoutException:
            #     logger.error("页面主体加载超时")
            #     self._save_responses()
            #     return False

            self._random_sleep(5,6)

            # 获取下拉框元素
            select_element = self._find_select_element()
            if not select_element:
                return False

            # 获取下拉框
            dropdowns = self._get_dropdowns(select_element)
            if not dropdowns:
                return False

            first_dropdown, second_dropdown = dropdowns

            # 处理第一个下拉框
            first_options_result = self._get_dropdown_options(first_dropdown)
            if not first_options_result:
                return False

            first_options, self.first_option_texts = first_options_result

            # 遍历第一个下拉框的每个选项
            for first_idx, first_option in enumerate(first_options):
                if not self._process_first_dropdown_option(first_dropdown, first_option, first_idx, second_dropdown):
                    continue

            # 测试版本 - 只取第一个选项
            # first_option = first_options[0]  # 只取第一个选项
            # first_idx = 0
            # if not self._process_first_dropdown_option(first_dropdown, first_option, first_idx, second_dropdown):
            #     return False


            return True
        except Exception as e:
            logger.error(f"处理级联下拉框时发生错误: {str(e)}")
            return False

    def _find_select_element(self) -> Optional[Any]:
        """查找select元素
        
        Returns:
            找到的select元素，如果未找到则返回None
        """
        select_selectors = [
            (By.CLASS_NAME, "select"),
            (By.XPATH, "//div[contains(@class, 'select')]"),
            (By.XPATH, "//div[contains(@class, 'select_box')]"),
            (By.XPATH, "//div[contains(@class, 'el-select')]")
        ]

        for by, selector in select_selectors:
            try:
                logger.info(f"尝试定位select元素: {selector}")
                select_element = self.wait.until(
                    EC.presence_of_element_located((by, selector))
                )
                if select_element:
                    logger.info(f"成功找到select元素: {selector}")
                    return select_element
            except TimeoutException:
                logger.warning(f"使用选择器 {selector} 未找到元素")
                continue

        logger.error("无法找到select元素")
        self._save_page_source_for_debug()
        return None

    def _save_page_source_for_debug(self) -> None:
        """保存页面源码以供调试"""
        page_source = self.driver.page_source
        timestamp = time.strftime("%H%M%S")
        debug_file = os.path.join(self.config.data_date_dir, f'page_source_debug_{timestamp}.html')
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(page_source)
        logger.info(f"已保存页面源码到 {debug_file}")

    def _get_dropdowns(self, select_element: Any) -> Optional[tuple]:
        """获取下拉框元素
        
        Args:
            select_element: select元素
            
        Returns:
            包含两个下拉框的元组，如果获取失败则返回None
        """
        try:
            dropdowns = select_element.find_elements(By.CLASS_NAME, "select_box")
            if len(dropdowns) < 2:
                logger.error(f"找到的下拉框数量不足: {len(dropdowns)}")
                return None
            logger.info("成功获取两个下拉框元素")
            return dropdowns[0], dropdowns[1]
        except Exception as e:
            logger.error(f"获取下拉框元素时发生错误: {str(e)}")
            return None

    def _get_dropdown_options(self, dropdown: Any) -> Optional[tuple]:
        """获取下拉框选项
        
        Args:
            dropdown: 下拉框元素
            
        Returns:
            包含选项元素列表和选项文本字典的元组，如果获取失败则返回None
        """
        try:
            dropdown.click()
            logger.info("点击下拉框完成")
            self._random_sleep(2, 2)
            options_container = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//div[@x-placement='bottom-start']//ul[@class='el-scrollbar__view el-select-dropdown__list']"))
            )
            options = options_container.find_elements(By.CLASS_NAME, "el-select-dropdown__item")
            
            if not options:
                logger.error("下拉框没有选项")
                return None
            
            # 保存选项文本
            option_texts = {}
            for idx, option in enumerate(options):
                try:
                    option_text = option.find_element(By.CLASS_NAME, "readiock").find_element(By.TAG_NAME, "span").text
                    option_texts[idx] = option_text
                    logger.info(f"保存选项 {idx} 的文本: {option_text}")
                except Exception as e:
                    logger.warning(f"获取选项 {idx} 的文本失败: {str(e)}")
                    option_texts[idx] = f"未知选项_{idx}"
                
            logger.info(f"成功获取下拉框选项，共 {len(options)} 个选项")
            return options, option_texts
        except Exception as e:
            logger.error(f"获取下拉框选项时发生错误: {str(e)}")
            return None

    def _process_first_dropdown_option(self, first_dropdown: Any, first_option: Any, first_idx: int, second_dropdown: Any) -> bool:
        """处理第一个下拉框的选项
        
        Args:
            first_dropdown: 第一个下拉框元素
            first_option: 当前选项元素
            first_idx: 选项索引
            second_dropdown: 第二个下拉框元素
            
        Returns:
            是否成功处理该选项
        """
        try:
            # 获取选项文本
            first_option_text = self.first_option_texts.get(first_idx, f"未知选项_{first_idx}")
            logger.info(f"使用保存的第一个下拉框选项文本: {first_option_text}")

            # 选择选项
            if not self._select_dropdown_option(first_dropdown, first_option, first_idx):
                return False

            # 处理第二个下拉框
            second_options_result = self._get_dropdown_options(second_dropdown)
            if not second_options_result:
                return False

            second_options, self.second_option_texts = second_options_result

            # 遍历第二个下拉框的每个选项
            for second_idx, second_option in enumerate(second_options):
                if not self._process_second_dropdown_option(second_dropdown, second_option, second_idx, first_idx):
                    continue

            # second_option = second_options[0]  # 只取第一个选项
            # second_idx = 0
            # if not self._process_second_dropdown_option(second_dropdown, second_option, second_idx, first_idx):
            #     return False

            return True
        except Exception as e:
            logger.error(f"处理第一个下拉框选项时发生错误: {str(e)}")
            return False

    def _select_dropdown_option(self, dropdown: Any, option: Any, index: int) -> bool:
        """选择下拉框选项
        
        Args:
            dropdown: 下拉框元素
            option: 选项元素
            index: 选项索引
            
        Returns:
            是否成功选择选项
        """
        try:
            # 滚动到下拉框位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", dropdown)
            dropdown.click()
            
            # 等待下拉选项出现
            options_container = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//div[@x-placement='bottom-start']//ul[@class='el-scrollbar__view el-select-dropdown__list']"))
            )
            self._random_sleep()
            
            # 滚动到下拉选项容器位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", options_container)
            
            # 尝试多种方式点击选项
            try:
                option.click()
                logger.info("使用直接点击方式选择选项")
            except Exception as e1:
                logger.warning(f"直接点击失败，尝试JavaScript点击")
                try:
                    self.driver.execute_script("arguments[0].click();", option)
                    logger.info("使用JavaScript点击方式选择选项")
                except Exception as e2:
                    logger.warning(f"JavaScript点击失败，尝试通过索引点击")
                    try:
                        options = options_container.find_elements(By.CLASS_NAME, "el-select-dropdown__item")
                        if index < len(options):
                            self.driver.execute_script("arguments[0].click();", options[index])
                            logger.info("使用索引点击方式选择选项")
                        else:
                            logger.error(f"选项索引 {index} 超出范围")
                            return False
                    except Exception as e3:
                        logger.error(f"所有点击方式都失败")
                        return False
            
            self._random_sleep()
            return True
        except Exception as e:
            logger.error(f"选择下拉框选项时发生错误: {str(e)}")
            return False

    def _process_second_dropdown_option(self, second_dropdown: Any, second_option: Any, second_idx: int, first_idx: int) -> bool:
        """处理第二个下拉框的选项
        
        Args:
            second_dropdown: 第二个下拉框元素
            second_option: 当前选项元素
            second_idx: 选项索引
            first_idx: 第一个下拉框的选项索引
            
        Returns:
            是否成功处理该选项
        """
        try:
            # 获取选项文本
            first_option_text = self.first_option_texts.get(first_idx, f"未知选项_{first_idx}")
            second_option_text = self.second_option_texts.get(second_idx, f"未知选项_{second_idx}")
            logger.info(f"使用保存的选项文本: {first_option_text} -> {second_option_text}")

            # 选择选项
            if not self._select_dropdown_option(second_dropdown, second_option, second_idx):
                return False

            # 判断页面是否有<div class="shujvqu">
            try:
                WebDriverWait(self.driver, 5).until(lambda d: d.find_element(By.CSS_SELECTOR, 'div.shujvqu'))
            except Exception:
                logger.warning("未检测到<div class='shujvqu'>，跳过本次采集")
                return True  # 跳过后续采集，继续下一个二级选项

            # 滚动到底部加载数据
            self._scroll_to_bottom()

            # 保存当前页面HTML
            html_saver.save_selenium_html(
                driver=self.driver,
                province_code=self.province_code,
                province_name=self.province_name,
                operator="unicom",
                page_info=f"{first_option_text}_{second_option_text}"
            )

            # 捕获网络请求
            requests = self._capture_network_requests()

            # 处理请求并保存数据
            if requests:
                # 使用下拉框文本作为数据键
                data_key = f"{first_option_text}_{second_option_text}"
                self.captured_responses[data_key] = []
                last_request = requests[-1]
                try:
                    if 'requestId' in last_request:
                        resp = self.driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': last_request['requestId']})
                        if 'body' in resp:
                            try:
                                response_data = json.loads(resp['body'])
                                self.captured_responses[data_key] = response_data
                                logger.info(f"已保存数据: {data_key}")
                            except json.JSONDecodeError:
                                self.captured_responses[data_key] = resp['body']
                                logger.info(f"已保存原始数据: {data_key}")
                except WebDriverException:
                    logger.error(f"获取响应数据失败: {data_key}")
                    pass

            # 滚动到顶部，为下一次选择做准备
            self._scroll_to_top()

            return True
        except Exception as e:
            logger.error(f"处理第二个下拉框选项时发生错误: {str(e)}")
            return False

    def run(self) -> None:
        """运行爬虫主流程，包含重试机制"""
        max_retries = 3
        retry_count = 0
        last_error = None
        self.all_success = False  # 标志：只有全部成功才压缩
        
        while retry_count < max_retries:
            # 每次重试前清空已生成文件列表，避免旧文件被加入zip
            self.generated_files = []
            try:
                if retry_count > 0:
                    logger.info(f"第 {retry_count} 次重试...")
                
                self.setup_driver()

                # 打开目标URL
                logger.info("正在打开目标页面...")
                self.driver.get(self.config.main_url)
                self._random_sleep(2, 4)

                # 等待shujvqu元素出现
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.CLASS_NAME, "shujvqu"))
                    )
                except TimeoutException:
                    logger.error("等待页面加载超时")
                    self._save_responses()
                    raise Exception("页面加载超时")

                # 保存初始页面HTML
                html_saver.save_selenium_html(
                    driver=self.driver,
                    province_code=self.province_code,
                    province_name=self.province_name,
                    operator="unicom",
                    page_info="初始页面"
                )

                # # 全国资费数据
                if self._select_and_scrape_all_combinations():
                    logger.info("全国资费数据采集完成")
                    self._save_responses("全国资费")
                else:
                    logger.error("全国资费数据采集失败")
                    raise Exception("全国资费数据采集失败")

                self.driver.refresh()
                self._random_sleep(2, 2)
                # 处理本省资费数据
                if self._find_and_click_local_tariff():
                    logger.info("开始采集本省资费数据...")
                    self._random_sleep(2, 2)
                else:
                    logger.error("未找到本省资费标签")
                    raise Exception("未找到本省资费标签")

                # 只有全部成功才压缩文件
                if self.all_success:
                    self.province_name = self.provic_dict[self.province_code]
                    self._compress_json_files()
                    logger.info("数据采集完成")
                else:
                    logger.info("采集未全部成功，不生成压缩包")
                
                # 如果执行到这里没有异常，说明成功完成，跳出重试循环
                break
                
            except Exception as e:
                last_error = e
                retry_count += 1
                logger.error(f"第 {retry_count} 次执行失败: {str(e)}")
                
                try:
                    # 保存当前状态
                    self._save_responses(f"retry_{retry_count}")
                    logger.info(f"第 {retry_count} 次重试时已保存已采集的数据")
                except Exception as save_error:
                    logger.error(f"保存数据时发生错误: {str(save_error)}")
                
                if retry_count < max_retries:
                    logger.info(f"等待 5 秒后开始第 {retry_count + 1} 次重试...")
                    time.sleep(5)
                else:
                    logger.error(f"已达到最大重试次数 ({max_retries})，程序终止")
                
            finally:
                try:
                    if self.driver:
                        self.driver.quit()
                    logger.info("浏览器已关闭")
                except Exception as e:
                    logger.error(f"关闭浏览器时发生错误: {str(e)}")
        
        # 如果所有重试都失败了，记录最终状态
        if retry_count == max_retries and last_error:
            logger.error(f"所有重试都失败，最后一次错误: {str(last_error)}")
            try:
                self._save_responses("final_error")
                logger.info("已保存最终错误状态的数据")
            except Exception as save_error:
                logger.error(f"保存最终错误状态时发生错误: {str(save_error)}")

def callback(callback_url: str, file: str, crawler_url: str, status: str = '200') -> dict:
    """回调函数，将zip文件通过FormData发送到指定的URL
    
    Args:
        callback_url: 回调URL地址
        file: zip文件路径
        crawler_url: 爬虫URL地址
        status: 状态码，默认为'200'，失败时为'500'
        
    Returns:
        dict: 回调响应的JSON数据
    """
    return sync_callback(callback_url, file, crawler_url, status)

async def main(provinces: str = None, callback_url: str = None) -> str:
    """主函数
    
    Args:
        provinces: 省份编码字符串，如果为None则使用默认值
        callback_url: 回调URL，用于发送zip文件
        
    Returns:
        str: 生成的zip文件路径
    """
    scraper = None
    screen_recorder = None
    mp4_path = None
    try:
        # 创建配置
        config = ScraperConfig(provinces=[provinces] if provinces else None)
        
        # 初始化爬虫
        scraper = UnicomScraper(config)
        
        # ========== 路径生成开始 ==========
        import random
        from datetime import datetime
        province_code = provinces or 'unknown'
        rand_str = str(random.randint(1000, 9999))
        zip_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "zip")
        zip_filename = f"{province_code}_unicom_{rand_str}_资费数据.zip"
        zip_filepath = os.path.join(zip_dir, zip_filename)
        mp4_path = os.path.splitext(zip_filepath)[0] + ".mp4"
        # ========== 路径生成结束 ==========
        
        # ========== 录屏集成开始 ==========
        screen_recorder = None
        try:
            # 检查是否启用录屏
            if is_screen_recording_enabled():
                # 调整浏览器窗口位置和大小
                window_width, window_height = get_adaptive_window_size()
                scraper.driver.set_window_size(window_width, window_height)
                
                # 等待浏览器完全打开并加载页面
                time.sleep(3)  # 等待浏览器稳定
                logger.info("浏览器页面加载完成")
                
                # 调整Anaconda Prompt窗口到右半屏
                anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
                split_window_to_right_half(anaconda_window_titles, show_debug=True)
                
                # 激活并置顶Anaconda Prompt窗口
                activate_window(anaconda_window_titles, show_debug=True)
                
                logger.info("Anaconda Prompt窗口已调整到右半屏并置顶")
                
                screen_recorder = ScreenRecorder(output_file=mp4_path, capture_method='dshow')
                screen_recorder.start_recording()
                logger.info(f"录屏已开始，文件: {mp4_path}")
            else:
                logger.info("录屏功能已禁用")
        except Exception as e:
            logger.error(f"录屏启动失败: {str(e)}")
            screen_recorder = None
        # ========== 录屏集成结束 ==========
        
        # 运行爬虫
        scraper.run()
        
        # 获取生成的zip文件路径
        if scraper.generated_files:
            zip_file = scraper.generated_files[0]  # 获取zip文件路径
            if os.path.exists(zip_file):
                logger.info(f"成功生成zip文件: {zip_file}")
                
                # 如果提供了回调URL，则发送文件
                if callback_url:
                    # 检查是否有错误状态
                    status = '500' if any('error' in f.lower() or 'retry' in f.lower() for f in scraper.generated_files) else '200'
                    callback_result = callback(
                        callback_url,
                        zip_file,
                        f"https://img.client.10010.com/zifeizhuanquwt/index.html?time=1747278766317#/home?procode={provinces}",
                        status
                    )
                    logger.info(f"回调结果: {callback_result}")
                
                return zip_file
            else:
                logger.error(f"zip文件不存在: {zip_file}")
                return None
        else:
            logger.error("未生成任何文件")
            return None
        
    except KeyboardInterrupt:
        logger.info("检测到键盘中断，正在退出...")
        return None
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        return None
    finally:
        # 确保关闭浏览器
        if scraper and scraper.driver:
            try:
                scraper.driver.quit()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {str(e)}")
        
        # 停止录屏
        if screen_recorder:
            try:
                screen_recorder.stop_recording()
                logger.info(f"录屏已结束，文件: {mp4_path}")
            except Exception as e:
                logger.error(f"录屏结束失败: {str(e)}")
        
        # 获取当前进程
        current_process = psutil.Process()
        
        # 获取所有子进程
        children = current_process.children(recursive=True)
        
        # 强制终止所有子进程
        for child in children:
            try:
                child.kill()
                logger.info(f"已终止子进程: {child.pid}")
            except psutil.NoSuchProcess:
                pass
            except Exception as e:
                logger.error(f"终止子进程 {child.pid} 时发生错误: {str(e)}")
        
        # 获取所有线程
        threads = threading.enumerate()
        
        # 终止除主线程外的所有线程
        for thread in threads:
            if thread != threading.main_thread():
                try:
                    thread.daemon = True
                    logger.info(f"已将线程标记为守护线程: {thread.name}")
                except Exception as e:
                    logger.error(f"处理线程 {thread.name} 时发生错误: {str(e)}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main(provinces='090'))





