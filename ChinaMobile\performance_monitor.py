#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控脚本 - 实时监控解析性能并提供优化建议
"""

import time
import psutil
import threading
from collections import defaultdict
from datetime import datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.metrics = defaultdict(list)
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        print("性能监控已启动...")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("性能监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # 记录指标
                current_time = time.time()
                self.metrics['cpu'].append((current_time, cpu_percent))
                self.metrics['memory'].append((current_time, memory.percent))
                
                # 每10秒输出一次状态
                if int(current_time - self.start_time) % 10 == 0:
                    print(f"CPU使用率: {cpu_percent:.1f}%, 内存使用率: {memory.percent:.1f}%")
                    
            except Exception as e:
                print(f"监控错误: {e}")
                
    def get_performance_report(self):
        """获取性能报告"""
        if not self.metrics:
            return "无监控数据"
            
        report = []
        report.append("=== 性能监控报告 ===")
        
        # CPU使用情况
        if self.metrics['cpu']:
            cpu_values = [v[1] for v in self.metrics['cpu']]
            avg_cpu = sum(cpu_values) / len(cpu_values)
            max_cpu = max(cpu_values)
            report.append(f"CPU使用率 - 平均: {avg_cpu:.1f}%, 最高: {max_cpu:.1f}%")
            
        # 内存使用情况
        if self.metrics['memory']:
            memory_values = [v[1] for v in self.metrics['memory']]
            avg_memory = sum(memory_values) / len(memory_values)
            max_memory = max(memory_values)
            report.append(f"内存使用率 - 平均: {avg_memory:.1f}%, 最高: {max_memory:.1f}%")
            
        # 优化建议
        report.append("\n=== 优化建议 ===")
        if avg_cpu > 80:
            report.append("⚠️  CPU使用率过高，建议减少线程数或优化代码")
        if avg_memory > 80:
            report.append("⚠️  内存使用率过高，建议检查内存泄漏")
        if avg_cpu < 30:
            report.append("💡 CPU使用率较低，可以适当增加线程数")
            
        return "\n".join(report)

# 全局监控器实例
monitor = PerformanceMonitor()

def start_performance_monitoring():
    """启动性能监控"""
    monitor.start_monitoring()

def stop_performance_monitoring():
    """停止性能监控并输出报告"""
    monitor.stop_monitoring()
    print(monitor.get_performance_report())

if __name__ == "__main__":
    # 测试监控功能
    start_performance_monitoring()
    time.sleep(30)  # 监控30秒
    stop_performance_monitoring() 