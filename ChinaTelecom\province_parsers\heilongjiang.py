from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_heilongjiang_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析黑龙江资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析{menu_type}的页面内容...")
        
        # 等待列表区域加载
        await page.wait_for_selector("div#list", timeout=30000)
        
        # 获取所有产品项
        products = await page.query_selector_all("div#ProductItem")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        logger.info(f"开始解析{menu_type}产品，共{total_products}个")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.title > h3")
                code_elem = await product.query_selector("div.title > span")
                
                title = await title_elem.text_content() if title_elem else "未知标题"
                code = await code_elem.text_content() if code_elem else "未知编号"
                
                # 提取方案编号，删除空格
                if "方案编号：" in code:
                    code = code.split("方案编号：")[1].strip()
                code = code.replace(" ", "")
                
                # 获取基本信息
                basic_info = {}
                base_info_div = await product.query_selector("div.base")
                if base_info_div:
                    # 获取所有基本信息span
                    info_spans = await base_info_div.query_selector_all("span")
                    for span in info_spans:
                        text = await span.text_content()
                        if "：" in text:
                            key, value = text.split("：", 1)
                            basic_info[key.strip()] = value.strip()
                
                # 获取服务内容
                service_content = {}
                table = await product.query_selector("table")
                if table:
                    # 获取表头
                    header_cells = await table.query_selector_all("thead td")
                    header_names = [await cell.text_content() for cell in header_cells if await cell.text_content()]
                    
                    # 获取数值
                    value_cells = await table.query_selector_all("tbody td")
                    value_texts = [await cell.text_content() for cell in value_cells if await cell.text_content()]
                    
                    # 将表头和数值配对
                    min_len = min(len(header_names), len(value_texts))
                    for i in range(min_len):
                        service_content[header_names[i].strip()] = value_texts[i].strip()
                
                # 获取备注信息
                remark_elem = await product.query_selector("div.content.text-overflow")
                remark = await remark_elem.text_content() if remark_elem else ""
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_heilongjiang_menu_config() -> dict:
    """获取黑龙江的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div#Menu",
        'menu_selector': "div#Menu > div > span",
        'list_selector': "div#index > div#list"
    } 