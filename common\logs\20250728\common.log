2025-07-28 16:01:41 - MainProcess - 32076 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:01:42 - MainProcess - 32076 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:01:42 - MainProcess - 32076 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:01:42 - MainProcess - 32076 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:02:06 - MainProcess - 32076 - common - WARNING - [screen_record.py:60] - ffmpeg 进程已提前退出，无法发送退出命令。
2025-07-28 16:02:12 - MainProcess - 20916 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:02:13 - MainProcess - 20916 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:02:13 - MainProcess - 20916 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:02:13 - MainProcess - 20916 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:04:58 - MainProcess - 5508 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:04:59 - MainProcess - 5508 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:04:59 - MainProcess - 5508 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:04:59 - MainProcess - 5508 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:05:43 - MainProcess - 15576 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:05:44 - MainProcess - 15576 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:05:44 - MainProcess - 15576 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:05:44 - MainProcess - 15576 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:06:20 - MainProcess - 41820 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:18:37 - MainProcess - 20644 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:18:38 - MainProcess - 20644 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:18:38 - MainProcess - 20644 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:18:38 - MainProcess - 20644 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:23:14 - MainProcess - 50116 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:23:15 - MainProcess - 50116 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:23:15 - MainProcess - 50116 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:23:15 - MainProcess - 50116 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 16:27:42 - MainProcess - 20036 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250728
2025-07-28 16:27:42 - MainProcess - 20036 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-28 16:27:42 - MainProcess - 20036 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-28 16:27:42 - MainProcess - 20036 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-28 17:09:24 - MainProcess - 20036 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaBroadnet\zip\广东省_broadnet_1545_资费数据_retry1.mp4
