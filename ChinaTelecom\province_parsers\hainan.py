from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_hainan_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析海南资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析海南{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.list-box", timeout=10000)
        
        # 获取所有产品项
        products = await page.query_selector_all("div.list-box > div")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        logger.info(f"开始解析{menu_type}产品，共{total_products}个")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.list-box-hd > div.title")
                number_elem = await product.query_selector("div.list-box-hd > div.number")
                
                if not title_elem:
                    logger.error(f"第 {index} 个产品未找到标题元素")
                    continue
                
                title = await title_elem.text_content()
                code = await number_elem.text_content() if number_elem else ""
                code = code.replace("方案编号：", "").strip()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"方案编号: {code}")
                
                # 获取基本信息
                basic_info = {}
                info_rows = await product.query_selector_all("div.base-con > div > p")
                for row in info_rows:
                    try:
                        text = await row.text_content()
                        if "：" in text:
                            label, value = text.split("：", 1)
                            basic_info[label.strip()] = value.strip()
                            logger.info(f"基本信息 - {label.strip()}: {value.strip()}")
                    except Exception as e:
                        logger.error(f"解析基本信息行时发生错误: {str(e)}")
                
                # 获取服务内容
                service_content = {}
                table_rows = await product.query_selector_all("div.teble table tbody tr")
                for row in table_rows:
                    try:
                        th = await row.query_selector("th")
                        td = await row.query_selector("td")
                        if th and td:
                            name = await th.text_content()
                            value = await td.text_content()
                            service_content[name.strip()] = value.strip()
                            logger.info(f"服务内容 - {name.strip()}: {value.strip()}")
                    except Exception as e:
                        logger.error(f"解析服务内容行时发生错误: {str(e)}")
                
                # 获取备注信息
                remark = ""
                remark_elem = await product.query_selector("div.desc > p.p2")
                if remark_elem:
                    remark = await remark_elem.text_content()
                    logger.info(f"备注信息: {remark}")
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_hainan_menu_config() -> dict:
    """获取海南的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.fix-box",
        'menu_selector': "div.menu > div.menu-item",
        'list_selector': "div.list-box"
    } 