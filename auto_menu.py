import os
import shutil
import subprocess
from typing import List
from datetime import datetime

def get_operator_dirs() -> List[str]:
    """获取所有运营商目录"""
    return [
        'ChinaTelecom',
        'ChinaMobile',
        'ChinaUnicom',
        'ChinaBroadnet'
    ]

def get_cleanup_dirs(operator_dir: str) -> List[str]:
    """获取需要清理的目录列表"""
    return [
        os.path.join(operator_dir, 'data'),
        os.path.join(operator_dir, 'zip'),
        os.path.join(operator_dir, 'logs')
    ]

def count_files(directory: str) -> int:
    """计算目录中的文件数量"""
    total = 0
    for _, _, files in os.walk(directory):
        total += len(files)
    return total

def cleanup_directories(directories: List[str], dry_run: bool = False) -> None:
    """清理指定目录
    
    Args:
        directories: 要清理的目录列表
        dry_run: 是否只显示要删除的内容而不实际删除
    """
    for directory in directories:
        if not os.path.exists(directory):
            print(f"目录不存在: {directory}")
            continue
            
        try:
            if dry_run:
                # 计算要删除的文件数量
                print(f"正在计算 {directory} 中的文件...")
                file_count = count_files(directory)
                print(f"将删除目录 {directory} 中的 {file_count} 个文件")
            else:
                # 清空目录但保留目录本身
                print(f"正在清理 {directory}...")
                for item in os.listdir(directory):
                    item_path = os.path.join(directory, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                print(f"已清空目录: {directory}")
        except Exception as e:
            print(f"清理目录 {directory} 时发生错误: {str(e)}")

def create_rar_archive() -> None:
    """创建当前目录的RAR压缩包"""
    try:
        # 获取当前时间戳
        timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
        # 使用当前目录的绝对路径来保存RAR文件
        current_dir = os.path.abspath(os.getcwd())
        archive_name = os.path.join(current_dir, f"xty_{timestamp}.rar")
        
        # 使用WinRAR命令行工具创建压缩包
        rar_path = r"C:\Program Files\WinRAR\Rar.exe"
        if not os.path.exists(rar_path):
            print("错误: 未找到WinRAR程序，请确保已安装WinRAR")
            return
            
        # 构建WinRAR命令
        cmd = [
            rar_path,
            "a",  # 添加文件到压缩包
            "-r",  # 递归处理子目录
            "-ep1",  # 排除基本目录
            archive_name,  # 压缩包完整路径
            "."  # 当前目录
        ]
        
        print(f"正在创建压缩包: {archive_name}")
        subprocess.run(cmd, check=True)
        print(f"压缩包创建成功: {archive_name}")
        
    except subprocess.CalledProcessError as e:
        print(f"创建压缩包时出错: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")

def show_main_menu() -> None:
    """显示主菜单"""
    os.system('cls' if os.name == 'nt' else 'clear')
    print("\n=== 运营商数据清理工具 ===")
    print("1. 清理数据")
    print("2. 启动指定端口浏览器（广电）")
    print("3. 打包当前目录")
    print("0. 退出程序")
    print("=========================")

def show_cleanup_menu() -> None:
    """显示清理数据子菜单"""
    os.system('cls' if os.name == 'nt' else 'clear')
    print("\n=== 选择要清理的运营商 ===")
    operators = get_operator_dirs()
    for i, operator in enumerate(operators, 1):
        print(f"{i}. {operator}")
    print("5. 清理所有运营商")
    print("6. 返回主菜单")
    print("=========================")

def get_valid_input(prompt: str, valid_choices: List[str]) -> str:
    """获取有效的用户输入"""
    while True:
        choice = input(prompt).strip()
        if choice in valid_choices:
            return choice
        print("无效的选择，请重试")

def launch_chrome_browser():
    """启动指定端口的Chrome浏览器"""
    try:
        chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        user_data_dir = r"E:\software\ChromeUserData"
        
        # 使用subprocess.Popen在后台启动浏览器
        subprocess.Popen([
            chrome_path,
            f"--remote-debugging-port=9220",
            f"--user-data-dir={user_data_dir}"
        ], 
        creationflags=subprocess.CREATE_NEW_CONSOLE)  # 在新窗口中启动
        
        print("浏览器启动命令已执行，请查看浏览器窗口。")
    except Exception as e:
        print(f"启动浏览器时出错: {str(e)}")

def interactive_menu() -> None:
    """交互式清理界面"""
    while True:
        show_main_menu()
        choice = get_valid_input("\n请选择操作 (0-3): ", ["0", "1", "2", "3"])
        if choice == "1":
            while True:
                show_cleanup_menu()
                sub_choice = get_valid_input("\n请选择要清理的运营商 (1-6): ", [str(i) for i in range(1, 7)])
                if sub_choice == "6":
                    break
                if sub_choice == "5":
                    if input("确定要清理所有运营商的数据吗？(y/n): ").lower() == 'y':
                        for operator in get_operator_dirs():
                            print(f"\n清理 {operator} 的数据...")
                            cleanup_directories(get_cleanup_dirs(operator))
                    else:
                        print("已取消操作")
                else:
                    operator = get_operator_dirs()[int(sub_choice) - 1]
                    if input(f"确定要清理 {operator} 的数据吗？(y/n): ").lower() == 'y':
                        cleanup_directories(get_cleanup_dirs(operator))
                    else:
                        print("已取消操作")
                input("\n按回车键继续...")
        elif choice == "2":
            print("正在启动指定端口浏览器...")
            launch_chrome_browser()
            input("\n按回车键继续...")
        elif choice == "3":
            print("正在打包当前目录...")
            create_rar_archive()
            input("\n按回车键继续...")
        elif choice == "0":
            print("感谢使用，再见！")
            break

def main():
    """主函数"""
    interactive_menu()

if __name__ == "__main__":
    main() 