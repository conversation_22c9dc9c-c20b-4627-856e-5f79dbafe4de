#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 用于验证解析速度优化效果
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from mobile_crawler import process_tariff_items, setup_driver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from common.logger_config import mobile_logger as logger

def test_performance():
    """测试解析性能"""
    logger.info("开始性能测试...")
    
    try:
        # 初始化浏览器
        driver = setup_driver()
        wait = WebDriverWait(driver, 60)
        
        # 访问测试页面（使用北京作为测试）
        test_url = "https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov=100"
        driver.get(test_url)
        
        # 等待页面加载
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "tariff-free")))
        logger.info("页面加载完成，开始性能测试")
        
        # 测试不同配置下的性能
        test_configs = [
            {"max_workers": 2, "description": "原始配置（2线程）"},
            {"max_workers": 4, "description": "优化配置（4线程）"},
            {"max_workers": 8, "description": "高性能配置（8线程）"},
        ]
        
        for config in test_configs:
            logger.info(f"\n=== 测试配置: {config['description']} ===")
            
            # 临时修改线程数配置
            import mobile_crawler
            original_max_workers = mobile_crawler.MAX_WORKERS
            mobile_crawler.MAX_WORKERS = config['max_workers']
            
            try:
                start_time = time.time()
                
                # 执行解析测试
                result = process_tariff_items(
                    driver=driver,
                    wait=wait,
                    source_text="测试来源",
                    type_text="测试类型",
                    province_id="100",
                    province_name="北京"
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                logger.info(f"配置: {config['description']}")
                logger.info(f"处理时间: {processing_time:.2f} 秒")
                logger.info(f"解析数据条数: {len(result) if result else 0}")
                logger.info(f"平均处理速度: {len(result)/processing_time:.2f} 条/秒" if result and processing_time > 0 else "N/A")
                
            finally:
                # 恢复原始配置
                mobile_crawler.MAX_WORKERS = original_max_workers
        
        logger.info("\n=== 性能测试完成 ===")
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    test_performance() 