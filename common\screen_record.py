import subprocess
import time
import sys
import os
from common.logger_config import logger
# from loguru import logger
import threading

class ScreenRecorder:
    def __init__(self, ffmpeg_path=None, output_file=None, framerate=10, resolution=None, crf=28, preset='veryfast', capture_method='gdigrab'):
        if output_file is None:
            raise ValueError('output_file 参数必须指定')
        self.ffmpeg_path = ffmpeg_path or r'E:\software\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe'  # 可根据实际情况调整
        self.capture_method = capture_method
        if capture_method == 'gdigrab':
            # 自动检测分辨率（仅Windows，其他平台可扩展）
            if resolution is None:
                try:
                    if sys.platform.startswith('win'):
                        import ctypes
                        user32 = ctypes.windll.user32
                        user32.SetProcessDPIAware()
                        width = user32.GetSystemMetrics(0)
                        height = user32.GetSystemMetrics(1)
                        self.resolution = f'{width}x{height}'
                    else:
                        self.resolution = '1280x720'  # 默认
                except Exception as e:
                    logger.error(f'自动检测分辨率失败，使用默认1280x720: {e}', exc_info=True)
                    self.resolution = '1280x720'
            else:
                self.resolution = resolution
        else:
            self.resolution = None
        self.output_file = output_file
        self.framerate = framerate
        self.crf = crf
        self.preset = preset
        self.process = None
        logger.info(f'录屏采集方式: {self.capture_method}')
        if self.capture_method == 'gdigrab':
            logger.info(f'录屏分辨率: {self.resolution}')
        elif self.capture_method == 'dshow':
            logger.info('请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases')

    def _log_ffmpeg_output(self, pipe):
        """采样输出ffmpeg日志：每隔20行输出一次，遇到关键字也输出"""
        keywords = [b'error', b'warning', b'frame', b'time']
        line_count = 0
        try:
            while True:
                line = pipe.readline()
                if not line:
                    break
                line_count += 1
                lower_line = line.lower()
                if any(k in lower_line for k in keywords) or line_count % 20 == 0:
                    try:
                        logger.info(f'[ffmpeg] {line.decode(errors="ignore").strip()}')
                    except Exception:
                        pass
        except Exception as e:
            logger.error(f'ffmpeg日志采样输出异常: {e}', exc_info=True)

    def start_recording(self):
        if self.capture_method == 'gdigrab':
            command = f'"{self.ffmpeg_path}" -y -rtbufsize 100M -f gdigrab -framerate {self.framerate} -video_size {self.resolution} -i desktop -vcodec libx264 -crf {self.crf} -preset {self.preset} -pix_fmt yuv420p "{self.output_file}"'
        elif self.capture_method == 'dshow':
            command = f'"{self.ffmpeg_path}" -y -rtbufsize 100M  -f dshow -framerate {self.framerate} -i video=screen-capture-recorder -vcodec libx264 -crf {self.crf} -preset {self.preset} -pix_fmt yuv420p "{self.output_file}"'
        else:
            raise ValueError(f'不支持的采集方式: {self.capture_method}')
        self.process = subprocess.Popen(
            command,
            shell=True,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        # 启动线程采样输出ffmpeg日志
        self._ffmpeg_log_thread = threading.Thread(target=self._log_ffmpeg_output, args=(self.process.stderr,), daemon=True)
        self._ffmpeg_log_thread.start()
        logger.info('开始录屏...')

    def stop_recording(self):
        if self.process:
            try:
                # 检查进程是否还在
                if self.process.poll() is not None:
                    logger.warning('ffmpeg 进程已提前退出，无法发送退出命令。')
                    return
                if self.process.stdin:
                    self.process.stdin.write('q\n'.encode('GBK'))
                    self.process.stdin.flush()
                else:
                    logger.warning('ffmpeg 进程的stdin已关闭，无法发送退出命令。')
                self.process.communicate()
                logger.info(f'录屏结束，文件已保存为 {self.output_file}')
            except Exception as e:
                logger.error(f'录屏结束失败: {e}', exc_info=True)
            finally:
                self.process = None

# if __name__ == '__main__':
#     ffmpeg_path = r'E:\software\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe'  # 替换为你的实际路径
#     recorder = ScreenRecorder(ffmpeg_path=ffmpeg_path,output_file='record_demo.mp4')
#     recorder.start_recording()
#     time.sleep(10)  # 录制10秒
#     recorder.stop_recording() 