2025-07-31 08:51:08 - MainProcess - 52092 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 08:51:10 - SpawnProcess-1 - 59452 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 08:51:22 - MainProcess - 40232 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 08:51:23 - MainProcess - 40232 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 08:51:23 - MainProcess - 40232 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 08:51:23 - MainProcess - 40232 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (192, 192)
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 08:51:37 - MainProcess - 40232 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 08:51:37 - MainProcess - 40232 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 08:51:37 - MainProcess - 40232 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 08:51:44 - MainProcess - 40232 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 08:51:44 - MainProcess - 40232 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 09:56:03 - MainProcess - 37892 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 09:56:05 - SpawnProcess-1 - 21088 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 09:56:08 - MainProcess - 10544 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 09:56:08 - MainProcess - 10544 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 09:56:08 - MainProcess - 10544 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 09:56:08 - MainProcess - 10544 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 09:56:20 - MainProcess - 10544 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 09:56:20 - MainProcess - 10544 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 09:56:20 - MainProcess - 10544 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 09:56:28 - MainProcess - 10544 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 09:56:28 - MainProcess - 10544 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:01:12 - MainProcess - 42792 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:01:14 - SpawnProcess-1 - 28388 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:01:16 - MainProcess - 15972 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:01:16 - MainProcess - 15972 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 10:01:16 - MainProcess - 15972 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 10:01:16 - MainProcess - 15972 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 10:01:22 - MainProcess - 15972 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:01:22 - MainProcess - 15972 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:01:22 - MainProcess - 15972 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:01:31 - MainProcess - 15972 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:01:31 - MainProcess - 15972 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 10:21:10 - MainProcess - 18316 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:21:12 - SpawnProcess-1 - 36756 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:21:13 - MainProcess - 13400 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:21:14 - MainProcess - 13400 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 10:21:14 - MainProcess - 13400 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 10:21:14 - MainProcess - 13400 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 10:21:20 - MainProcess - 13400 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:21:20 - MainProcess - 13400 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:21:20 - MainProcess - 13400 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 10:21:28 - MainProcess - 13400 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:21:28 - MainProcess - 13400 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:27:09 - MainProcess - 14244 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:27:10 - SpawnProcess-1 - 31004 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:27:13 - MainProcess - 20032 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 10:27:14 - MainProcess - 20032 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 10:27:14 - MainProcess - 20032 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 10:27:14 - MainProcess - 20032 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 10:27:20 - MainProcess - 20032 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:27:20 - MainProcess - 20032 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:27:20 - MainProcess - 20032 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 10:27:28 - MainProcess - 20032 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 10:27:28 - MainProcess - 20032 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 10:36:25 - MainProcess - 20032 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 98076 字节
2025-07-31 10:36:25 - MainProcess - 20032 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-07-31 10:36:26 - MainProcess - 20032 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\nx_telecom_4846_资费数据.mp4
2025-07-31 14:09:10 - MainProcess - 44516 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:09:12 - SpawnProcess-1 - 16856 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:09:16 - MainProcess - 12864 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:09:16 - MainProcess - 12864 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:09:16 - MainProcess - 12864 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:09:16 - MainProcess - 12864 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1038
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:09:22 - MainProcess - 12864 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:09:22 - MainProcess - 12864 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:09:22 - MainProcess - 12864 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:09:29 - MainProcess - 12864 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:09:29 - MainProcess - 12864 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:12:43 - MainProcess - 5944 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:12:44 - SpawnProcess-1 - 19144 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:12:46 - MainProcess - 36000 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:12:47 - MainProcess - 36000 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:12:47 - MainProcess - 36000 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:12:47 - MainProcess - 36000 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:12:52 - MainProcess - 36000 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:12:52 - MainProcess - 36000 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:12:52 - MainProcess - 36000 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:13:00 - MainProcess - 36000 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:13:00 - MainProcess - 36000 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:15:39 - MainProcess - 10628 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:15:41 - SpawnProcess-1 - 27128 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:15:46 - MainProcess - 25524 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:15:47 - MainProcess - 25524 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:15:47 - MainProcess - 25524 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:15:47 - MainProcess - 25524 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:15:52 - MainProcess - 25524 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:15:52 - MainProcess - 25524 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:15:52 - MainProcess - 25524 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:15:58 - MainProcess - 25524 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:15:58 - MainProcess - 25524 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:17:39 - MainProcess - 43172 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:17:41 - SpawnProcess-1 - 33316 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:17:45 - MainProcess - 7928 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:17:46 - MainProcess - 7928 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:17:46 - MainProcess - 7928 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:17:46 - MainProcess - 7928 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:17:52 - MainProcess - 7928 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:17:52 - MainProcess - 7928 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:17:52 - MainProcess - 7928 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:18:00 - MainProcess - 7928 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:18:00 - MainProcess - 7928 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:19:10 - MainProcess - 17592 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:19:12 - SpawnProcess-1 - 32356 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:19:14 - MainProcess - 54044 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:19:14 - MainProcess - 54044 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:19:14 - MainProcess - 54044 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:19:14 - MainProcess - 54044 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:19:20 - MainProcess - 54044 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-07-31 14:19:20 - MainProcess - 54044 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:19:20 - MainProcess - 54044 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:19:26 - MainProcess - 54044 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:19:26 - MainProcess - 54044 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:20:17 - MainProcess - 60544 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:20:19 - SpawnProcess-1 - 11056 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:20:21 - MainProcess - 19200 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:20:22 - MainProcess - 19200 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:20:22 - MainProcess - 19200 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:20:22 - MainProcess - 19200 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:20:28 - MainProcess - 19200 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:20:29 - MainProcess - 19200 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:20:29 - MainProcess - 19200 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:20:29 - MainProcess - 19200 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:20:41 - MainProcess - 19200 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:20:41 - MainProcess - 19200 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:21:58 - MainProcess - 11812 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:22:00 - SpawnProcess-1 - 51836 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:22:01 - MainProcess - 48172 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:22:01 - MainProcess - 48172 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:22:01 - MainProcess - 48172 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:22:01 - MainProcess - 48172 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:22:07 - MainProcess - 48172 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:22:07 - MainProcess - 48172 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:22:07 - MainProcess - 48172 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:22:14 - MainProcess - 48172 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:22:14 - MainProcess - 48172 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:26:06 - MainProcess - 48172 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 236217 字节
2025-07-31 14:26:06 - MainProcess - 48172 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-07-31 14:26:07 - MainProcess - 48172 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\bj_telecom_8534_资费数据.mp4
2025-07-31 14:29:20 - MainProcess - 21836 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:29:21 - SpawnProcess-1 - 39888 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:29:24 - MainProcess - 9236 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:29:25 - MainProcess - 9236 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:29:25 - MainProcess - 9236 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:29:25 - MainProcess - 9236 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:29:30 - MainProcess - 9236 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:29:30 - MainProcess - 9236 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:29:30 - MainProcess - 9236 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:29:36 - MainProcess - 9236 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:29:36 - MainProcess - 9236 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:33:33 - MainProcess - 9236 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 233588 字节
2025-07-31 14:33:33 - MainProcess - 9236 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-07-31 14:33:34 - MainProcess - 9236 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\bj_telecom_6948_资费数据.mp4
2025-07-31 14:37:25 - MainProcess - 42124 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:37:26 - SpawnProcess-1 - 51072 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:37:28 - MainProcess - 16076 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 14:37:29 - MainProcess - 16076 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 14:37:29 - MainProcess - 16076 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 14:37:29 - MainProcess - 16076 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 14:37:35 - MainProcess - 16076 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:37:35 - MainProcess - 16076 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:37:35 - MainProcess - 16076 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 14:37:41 - MainProcess - 16076 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 14:37:41 - MainProcess - 16076 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 14:41:40 - MainProcess - 16076 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 233420 字节
2025-07-31 14:41:40 - MainProcess - 16076 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-07-31 14:41:40 - MainProcess - 16076 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\bj_telecom_9863_资费数据.mp4
2025-07-31 15:07:01 - MainProcess - 34556 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:07:02 - MainProcess - 34556 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:07:02 - MainProcess - 34556 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:07:02 - MainProcess - 34556 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:07:13 - MainProcess - 34556 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:07:13 - MainProcess - 34556 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:07:13 - MainProcess - 34556 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:07:20 - MainProcess - 34556 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:07:20 - MainProcess - 34556 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:10:33 - MainProcess - 34556 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 63414 字节
2025-07-31 15:10:33 - MainProcess - 34556 - common.callback_utils - INFO - [callback_utils.py:166] - 收到响应状态码: 200
2025-07-31 15:10:34 - MainProcess - 34556 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\js_telecom_9309_资费数据.mp4
2025-07-31 15:14:00 - MainProcess - 57680 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:14:00 - MainProcess - 57680 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:14:00 - MainProcess - 57680 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:14:00 - MainProcess - 57680 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:14:06 - MainProcess - 57680 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:14:06 - MainProcess - 57680 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:14:06 - MainProcess - 57680 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:14:13 - MainProcess - 57680 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:14:13 - MainProcess - 57680 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:17:15 - MainProcess - 47000 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:17:16 - SpawnProcess-1 - 60392 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:17:18 - MainProcess - 12992 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:17:18 - MainProcess - 12992 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:17:18 - MainProcess - 12992 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:17:18 - MainProcess - 12992 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 571 x 1029
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (3100, 61)
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:17:24 - MainProcess - 12992 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-07-31 15:17:24 - MainProcess - 12992 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:17:24 - MainProcess - 12992 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:17:32 - MainProcess - 12992 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:17:32 - MainProcess - 12992 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:17:59 - MainProcess - 12992 - common - ERROR - [screen_record.py:70] - 录屏结束失败: unknown encoding: GBK
Traceback (most recent call last):
  File "D:\PythonProjects\xty\ChinaTelecom\telecom_crawler.py", line 1249, in main
    await spider.handle_menu_items()
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProjects\xty\common\screen_record.py", line 63, in stop_recording
    self.process.stdin.write('q\n'.encode('GBK'))
LookupError: unknown encoding: GBK
2025-07-31 15:18:12 - MainProcess - 40912 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:18:13 - SpawnProcess-1 - 18592 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:18:16 - MainProcess - 25676 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:18:17 - MainProcess - 25676 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:18:17 - MainProcess - 25676 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:18:17 - MainProcess - 25676 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 571 x 1029
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (3664, 113)
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:18:22 - MainProcess - 25676 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:18:22 - MainProcess - 25676 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:18:22 - MainProcess - 25676 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:18:28 - MainProcess - 25676 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:18:28 - MainProcess - 25676 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:19:19 - MainProcess - 25676 - common - INFO - [screen_record.py:68] - 录屏结束，文件已保存为 D:\PythonProjects\xty\ChinaTelecom\zip\js_telecom_2709_资费数据.mp4
2025-07-31 15:28:09 - MainProcess - 46916 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:28:10 - SpawnProcess-1 - 27040 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:28:12 - MainProcess - 28300 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:28:13 - MainProcess - 28300 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:28:13 - MainProcess - 28300 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:28:13 - MainProcess - 28300 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 569 x 1027
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (359, 11)
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:28:18 - MainProcess - 28300 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:28:18 - MainProcess - 28300 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:28:18 - MainProcess - 28300 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:28:25 - MainProcess - 28300 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:28:25 - MainProcess - 28300 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:33:46 - MainProcess - 50244 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:33:47 - SpawnProcess-1 - 9468 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:33:50 - MainProcess - 12924 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:33:50 - MainProcess - 12924 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:33:50 - MainProcess - 12924 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:33:50 - MainProcess - 12924 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:33:55 - MainProcess - 12924 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:33:55 - MainProcess - 12924 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1038
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:33:56 - MainProcess - 12924 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:33:56 - MainProcess - 12924 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:33:56 - MainProcess - 12924 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:34:04 - MainProcess - 12924 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:34:04 - MainProcess - 12924 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:35:28 - MainProcess - 32 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:35:29 - SpawnProcess-1 - 20052 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:35:31 - MainProcess - 37708 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 15:35:32 - MainProcess - 37708 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 15:35:32 - MainProcess - 37708 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 15:35:32 - MainProcess - 37708 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 15:35:38 - MainProcess - 37708 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 15:35:38 - MainProcess - 37708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:35:38 - MainProcess - 37708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 15:35:45 - MainProcess - 37708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 15:35:45 - MainProcess - 37708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:46:21 - MainProcess - 12808 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:46:23 - SpawnProcess-1 - 43708 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:46:50 - MainProcess - 6516 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:46:51 - MainProcess - 6516 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 16:46:51 - MainProcess - 6516 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 16:46:51 - MainProcess - 6516 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 16:46:58 - MainProcess - 6516 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:46:58 - MainProcess - 6516 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:46:58 - MainProcess - 6516 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 16:47:04 - MainProcess - 6516 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:47:04 - MainProcess - 6516 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:49:33 - MainProcess - 30392 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:49:34 - SpawnProcess-1 - 49164 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:49:38 - MainProcess - 34260 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:49:38 - MainProcess - 34260 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 16:49:38 - MainProcess - 34260 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 16:49:38 - MainProcess - 34260 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 16:49:44 - MainProcess - 34260 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:49:44 - MainProcess - 34260 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:49:44 - MainProcess - 34260 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 16:49:51 - MainProcess - 34260 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:49:51 - MainProcess - 34260 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 16:58:45 - MainProcess - 23180 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:58:47 - SpawnProcess-1 - 50944 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:58:51 - MainProcess - 45784 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 16:58:51 - MainProcess - 45784 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 16:58:51 - MainProcess - 45784 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 16:58:51 - MainProcess - 45784 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 16:58:58 - MainProcess - 45784 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:58:58 - MainProcess - 45784 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:58:58 - MainProcess - 45784 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 16:59:05 - MainProcess - 45784 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 16:59:05 - MainProcess - 45784 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 17:20:49 - MainProcess - 29356 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 17:20:50 - SpawnProcess-1 - 2676 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 17:20:54 - MainProcess - 57712 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250731
2025-07-31 17:20:54 - MainProcess - 57712 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-07-31 17:20:54 - MainProcess - 57712 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-07-31 17:20:54 - MainProcess - 57712 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-07-31 17:20:59 - MainProcess - 57712 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-07-31 17:20:59 - MainProcess - 57712 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 17:20:59 - MainProcess - 57712 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-07-31 17:21:06 - MainProcess - 57712 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-07-31 17:21:06 - MainProcess - 57712 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
