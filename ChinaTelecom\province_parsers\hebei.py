from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_hebei_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析河北资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析河北{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.index-box.js-product-container", timeout=10000)
        
        # 先最大化浏览器窗口
        logger.info("最大化浏览器窗口...")
        await page.evaluate("""() => {
            window.moveTo(0, 0);
            window.resizeTo(screen.availWidth, screen.availHeight);
        }""")
        await page.wait_for_timeout(2000)  # 等待窗口调整完成
        
        # 处理"加载更多"按钮
        logger.info("开始处理'加载更多'按钮...")
        max_retries = 200  # 增加重试次数，因为可能需要多次点击
        retry_count = 0
        last_product_count = 0
        same_count_times = 0
        
        while retry_count < max_retries:
            try:
                # 获取当前产品数量
                current_products = await page.query_selector_all("div.index-box.js-product-container > div")
                current_count = len(current_products)
                logger.info(f"当前加载产品数量: {current_count}")
                
                # 检查产品数量是否变化
                if current_count == last_product_count:
                    same_count_times += 1
                    logger.info(f"产品数量未变化，连续 {same_count_times} 次")
                    if same_count_times >= 3:  # 连续3次数量不变，认为加载完成
                        logger.info("产品数量连续3次未变化，认为加载完成")
                        break
                else:
                    same_count_times = 0
                    last_product_count = current_count
                    logger.info(f"发现新数据，当前总数: {current_count}")
                
                # 查找并点击"加载更多"按钮
                load_more_button = await page.query_selector("span.js-load-more")
                if not load_more_button:
                    logger.info("未找到'加载更多'按钮，可能已加载全部内容")
                    break
                
                # 确保按钮可见
                await load_more_button.wait_for_element_state('visible')
                
                # 点击按钮
                await load_more_button.click()
                logger.info("点击'加载更多'按钮")
                
                # 等待新内容加载
                await page.wait_for_timeout(2000)
                
                retry_count += 1
                logger.info(f"第 {retry_count} 次点击'加载更多'按钮")
                
            except Exception as e:
                logger.error(f"点击'加载更多'按钮时发生错误: {str(e)}")
                retry_count += 1
                continue
        
        logger.info("内容加载完成")
        
        # 获取所有产品项
        products = await page.query_selector_all("div.index-box.js-product-container > div")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        logger.info(f"开始解析{menu_type}产品，共{total_products}个")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.top > p.title")
                code_elem = await product.query_selector("div.content-top > p")
                
                if not title_elem or not code_elem:
                    logger.error(f"第 {index} 个产品未找到标题或编号元素")
                    continue
                
                title = await title_elem.text_content()
                code = await code_elem.text_content()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"产品编号: {code}")
                
                # 提取方案编号，删除空格
                if "方案编号：" in code:
                    code = code.split("方案编号：")[1].strip()
                code = code.replace(" ", "")
                
                # 获取基本信息
                basic_info = {}
                info_list = await product.query_selector_all("div.list-content > ul > li")
                for item in info_list:
                    label = await item.query_selector("p.label")
                    desc = await item.query_selector("p.desc")
                    if label and desc:
                        key = await label.text_content()
                        value = await desc.text_content()
                        basic_info[key.strip()] = value.strip()
                
                logger.info(f"基本信息: {basic_info}")
                
                # 获取服务内容
                service_content = {}
                table = await product.query_selector("div.table.small table")
                if table:
                    rows = await table.query_selector_all("tr")
                    for row in rows:
                        cells = await row.query_selector_all("td")
                        if len(cells) >= 2:
                            key = await cells[0].text_content()
                            value = await cells[1].text_content()
                            service_content[key.strip()] = value.strip()
                    
                    logger.info(f"服务内容: {service_content}")
                else:
                    logger.warning(f"第 {index} 个产品未找到服务内容表格")
                
                # 获取备注信息
                remark = ""
                remark_div = await product.query_selector("div.remark")
                if remark_div:
                    # 获取所有备注段落
                    remark_paragraphs = await remark_div.query_selector_all("p")
                    remark_texts = []
                    for p in remark_paragraphs:
                        text = await p.text_content()
                        if text.strip():
                            remark_texts.append(text.strip())
                    remark = "\n".join(remark_texts)
                    logger.info(f"备注信息: {remark[:100]}...")  # 只记录前100个字符
                else:
                    logger.warning(f"第 {index} 个产品未找到备注信息")
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_hebei_menu_config() -> dict:
    """获取河北的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "li.menu-item",
        'menu_selector': "li.menu-item",  # 直接选择所有menu-item类的li元素
        'list_selector': "div.index-box.js-product-container"
    } 