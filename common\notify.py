import requests
from common.logger_config import telecom_logger, mobile_logger, unicom_logger, broadnet_logger, logger

def notify_callback(province_name: str, ent_name: str, tariff_type: str, err_msg: str) -> bool:
    """
    向 http://192.168.101.21:8088/callback/notify 发送通知，并记录日志。
    :param province_name: 省份名称
    :param ent_name: 运营商名称
    :param tariff_type: 资费类型
    :param err_msg: 描述信息
    :return: 是否通知成功
    """
    url = "http://192.168.101.21:8088/callback/notify"
    payload = {
        "provinceName": province_name,
        "entName": ent_name,
        "tariffType": tariff_type,
        "errMsg": err_msg
    }
    # 选择合适的 logger
    ent_name_lower = (ent_name or '').lower()
    if '电信' in ent_name_lower or 'telecom' in ent_name_lower:
        log = telecom_logger
    elif '移动' in ent_name_lower or 'mobile' in ent_name_lower:
        log = mobile_logger
    elif '联通' in ent_name_lower or 'unicom' in ent_name_lower:
        log = unicom_logger
    elif '广电' in ent_name_lower or 'broadnet' in ent_name_lower:
        log = broadnet_logger
    else:
        log = logger
    log.info(f"[notify_callback] 通知参数: {payload}")
    try:
        response = requests.post(url, json=payload, timeout=5)
        response.raise_for_status()
        log.info(f"[notify_callback] 通知成功，响应: {response.status_code} {response.text}")
        return True
    except Exception as e:
        log.error(f"[notify_callback] 通知失败: {e}")
        return False 