<!-- Generated by SpecStory -->

# Callback execution in finally block (2025-06-23 01:04Z)

_**User**_

回调在finally中调用

---

_**Assistant**_

你希望将回调（callback）操作放到 `finally` 代码块中，这样无论主流程是否异常，回调都能被执行。当前 `callback` 只在重试次数用尽且有 `callback_url` 时才在 `else` 里调用。你想要它在 `finally` 里统一处理。

下面是我的计划：

1. 在 `finally` 代码块中，判断如果 `callback_url` 存在且任务未成功，则调用 `callback`，并传递合适的状态码（如 `"500"`）。
2. 如果任务成功（即提前 return True），则在 return 前调用 `callback`，传递 `"200"` 状态码和生成的 zip 文件路径。
3. 保证 `callback` 只被调用一次，且参数正确。

我会先定位 `main` 函数的 `finally` 代码块，并做如下调整：

- 在 `finally` 里，如果 `callback_url` 存在且任务未成功（如 return None），则调用 `callback(callback_url, "", "https://m.10099.com.cn/expensesNotice/#/home", "500")`。
- 在任务成功 return 前，调用 `callback`，传递正确的 zip 文件路径和 `"200"` 状态码。

我将进行代码修改。

---

Read file: ChinaBroadnet/broadnet_crawler.py

---

我已经定位到 `main` 函数的 `finally` 代码块和整个重试逻辑。你希望回调（callback）在 `finally` 里统一调用，无论任务是否成功。

**建议实现方式：**
- 用一个变量（如 `task_success`）标记任务是否成功，以及（如有）zip 文件路径。
- 在 `finally` 里，如果 `callback_url` 存在，调用 `callback`，根据 `task_success` 传递 `"200"` 或 `"500"`，以及 zip 路径或空字符串。

**具体调整点：**
1. 在 `main` 函数的 while 循环 try 块内，任务成功时设置 `task_success = True`，并保存 zip 路径（如有）。
2. 在 finally 里，判断 `callback_url`，调用 `callback`，只调用一次。
3. 移除 except 里对 callback 的调用，避免重复。

我将进行如下修改。

---

```diff
      retry_count = 0
      last_error = None
+     task_success = False
+     zip_file_path = ""
      
      # 处理省份参数
```

```diff
                  raise Exception("处理省份选择和数据抓取失败")
              
+             task_success = True
+             # 这里如果有zip文件路径可以赋值 zip_file_path
+             # zip_file_path = ...
              return True
                  
```

```diff
              else:
                  logger.error(f"已达到最大重试次数 ({max_retries})，任务失败")
-                 if callback_url:
-                     # 直接使用状态码 '500' 进行回调
-                     callback(callback_url, "", "https://m.10099.com.cn/expensesNotice/#/home", "500")
+                 # 不再在这里调用 callback
                  return None
                  
```

```diff
                  except Exception as e:
                      logger.error(f"关闭浏览器时出现错误: {str(e)}")
+             # 统一回调
+             if callback_url:
+                 status = "200" if task_success else "500"
+                 file_path = zip_file_path if task_success else ""
+                 try:
+                     callback(callback_url, file_path, "https://m.10099.com.cn/expensesNotice/#/home", status)
+                     logger.info(f"已在finally中回调，状态: {status}")
+                 except Exception as cb_e:
+                     logger.error(f"回调时出现异常: {str(cb_e)}")
      
      logger.info("自动化任务完成")
```

---

已将回调 callback 的调用统一放到 finally 代码块中，无论任务是否成功都会在 finally 里调用一次，并根据执行结果传递正确的状态码和文件路径。这样可以保证回调逻辑的统一和健壮性，避免重复或遗漏。

如需进一步完善 zip 文件路径的传递（如有生成），可在任务成功时赋值 `zip_file_path`。

如果还有其他细节需要调整，欢迎继续告知！

---

