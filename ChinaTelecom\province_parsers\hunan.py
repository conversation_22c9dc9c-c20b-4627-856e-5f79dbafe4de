from playwright.async_api import Page
from common.logger_config import telecom_logger as logger
import asyncio
import random

async def parse_hunan_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析湖南资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析{menu_type}的页面内容...")
        
        # 等待列表区域加载
        await page.wait_for_selector("div.van-list", timeout=10000)
        
        # 使用PageDown键滚动加载所有内容
        logger.info("开始使用PageDown键滚动加载所有内容...")
        no_change_count = 0  # 记录数据条数不变的次数
        last_count = 0  # 上次滚动后的数据条数
        max_no_change = 1  # 连续1次数据条数不变，认为加载完成
        
        while True:  # 不限制滚动次数
            try:
                # 先向上滚动一段距离
                for _ in range(1):
                    await page.keyboard.press("PageUp")
                    await asyncio.sleep(0.3)
                logger.info("向上滚动完成")
                
                # 再向下滚动
                for _ in range(100):  # 减少每次滚动的次数，增加稳定性
                    await page.keyboard.press("PageDown")
                    await asyncio.sleep(0.2)  # 每次滚动后短暂等待
                logger.info("向下滚动完成")
                
                # 等待新内容加载
                await asyncio.sleep(1)
                
                # 获取当前数据条数
                current_products = await page.query_selector_all("div.list-box > div")
                current_count = len(current_products)
                logger.info(f"当前数据条数: {current_count}")
                
                # 检查数据条数是否变化
                if current_count == last_count:
                    no_change_count += 1
                    logger.info(f"数据条数未变化，连续 {no_change_count} 次")
                    if no_change_count >= max_no_change:  # 连续1次数据条数不变，认为加载完成
                        logger.info(f"数据条数连续{max_no_change}次未变化，认为加载完成")
                        break
                else:
                    no_change_count = 0  # 重置计数器
                    logger.info(f"数据条数增加: {current_count - last_count}")
                
                last_count = current_count
                
                # 每5次循环暂停一下，避免过快
                if no_change_count % 5 == 0:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"滚动加载过程中发生错误: {str(e)}")
                # 如果发生错误，等待页面稳定
                await asyncio.sleep(2)
                continue
        
        # 滚动回顶部
        await page.keyboard.press("Home")
        await asyncio.sleep(1)
        
        # 获取所有产品项
        products = await page.query_selector_all("div.list-box > div")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        # 批量处理产品
        batch_size = 10  # 每批处理10个产品
        for batch_start in range(0, total_products, batch_size):
            batch_end = min(batch_start + batch_size, total_products)
            logger.info(f"开始处理第 {batch_start + 1} 到 {batch_end} 个产品")
            
            # 批量解析产品数据
            for index in range(batch_start, batch_end):
                try:
                    product = products[index]
                    # 解析产品数据
                    try:
                        product_data = await parse_product(product)
                        if product_data:
                            products_data.append(product_data)
                            logger.info(f"成功解析第 {index + 1}/{total_products} 个产品: {product_data['title']}")
                    except Exception as e:
                        logger.error(f"解析第 {index + 1}/{total_products} 个产品时发生错误: {str(e)}")
                except Exception as e:
                    logger.error(f"处理第 {index + 1}/{total_products} 个产品时发生错误: {str(e)}")
                    continue
            
            # 每批处理完成后暂停
            await asyncio.sleep(1)
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
            
            await asyncio.sleep(random.uniform(2, 3))
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

async def parse_product(product) -> dict:
    """解析单个产品数据
    
    Args:
        product: 产品元素
        
    Returns:
        dict: 产品数据
    """
    try:
        # 获取产品标题
        title_elem = await product.query_selector("p.title")
        if not title_elem:
            raise Exception("未找到产品标题元素")
        title = await title_elem.text_content()
        
        # 获取方案编号
        code_elem = await product.query_selector("p.small")
        code = ""
        if code_elem:
            code_text = await code_elem.text_content()
            code = code_text.replace("方案编号：", "").strip()
        
        # 获取基本信息
        basic_info = {}
        info_items = await product.query_selector_all("div.text p")
        for item in info_items:
            try:
                text = await item.text_content()
                if "：" in text:
                    label, value = text.split("：", 1)
                    basic_info[label] = value
            except Exception as e:
                logger.error(f"解析基本信息项时发生错误: {str(e)}")
                continue
        
        # 获取服务内容
        service_content = {}
        try:
            table = await product.query_selector("table")
            if table:
                headers = await table.query_selector_all("th")
                values = await table.query_selector_all("td")
                for header, value in zip(headers, values):
                    header_text = await header.text_content()
                    value_text = await value.text_content()
                    service_content[header_text] = value_text
        except Exception as e:
            logger.error(f"解析服务内容时发生错误: {str(e)}")
        
        # 获取备注信息
        remark = ""
        try:
            remark_elem = await product.query_selector("div.remark p.p2")
            if remark_elem:
                remark = await remark_elem.text_content()
        except Exception as e:
            logger.error(f"解析备注信息时发生错误: {str(e)}")
        
        # 构建产品数据
        return {
            "title": title,
            "code": code,
            "basic_info": basic_info,
            "service_content": service_content,
            "remark": remark
        }
        
    except Exception as e:
        logger.error(f"解析产品时发生错误: {str(e)}")
        raise

def get_hunan_menu_config() -> dict:
    """获取湖南的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.menu-box",
        'menu_selector': "div.top.clearfix > div",
        'list_selector': "div.list-box > div"
    } 