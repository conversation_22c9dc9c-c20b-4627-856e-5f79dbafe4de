from typing import List, Dict, Optional, Union, Any
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import time
import os
import json
from datetime import datetime
import sys
from pathlib import Path
import concurrent.futures
from collections import defaultdict

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from common.logger_config import mobile_logger as logger
import argparse
import zipfile
import random
from dataclasses import dataclass
from contextlib import contextmanager
import asyncio
import aiohttp
import aiofiles
from common.screen_record import ScreenRecorder  # 新增导入
# 导入窗口操作工具
from common.window_utils import split_window_to_right_half, move_window_to_bottom_right, get_adaptive_window_size, activate_window

# 导入公共回调方法
from common.callback_utils import async_callback
# 导入HTML保存工具
from common.html_saver import html_saver
# 导入配置加载工具
from common.config_loader import is_screen_recording_enabled

# 性能优化配置
MAX_WORKERS = 8  # 最大线程数
MIN_WORKERS = 4  # 最小线程数
ITEMS_PER_WORKER = 10  # 每个线程处理的项目数基准

# 激进性能优化配置
ENABLE_CACHE = True  # 启用缓存
ENABLE_BATCH_DOM = True  # 启用批量DOM查询
ENABLE_MINIMAL_LOGGING = True  # 启用最小日志
MAX_PROCESSING_TIME = 5  # 单个容器最大处理时间（秒）
SKIP_COMPLEX_CONTAINERS = True  # 跳过复杂容器

# 性能优化缓存
from functools import lru_cache
import hashlib

@dataclass
class ChromeOptions:
    """Chrome浏览器配置类"""
    headless: bool = False
    incognito: bool = True
    disable_gpu: bool = True
    disable_extensions: bool = True
    disable_popup_blocking: bool = True
    disable_cache: bool = True
    user_agent: Optional[str] = None
    proxy: Optional[str] = None
    window_size: Optional[tuple] = None

class DataManager:
    """数据管理器类，用于处理数据保存和文件管理"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """初始化数据管理器
        
        Args:
            base_dir: 基础目录路径，如果为None则使用当前文件所在目录
        """
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self.current_date = datetime.now().strftime('%Y%m%d')
        self.data_dir = self.base_dir / "data" / self.current_date
        self.zip_dir = self.base_dir / "zip" / self.current_date
        self.log_dir = self.base_dir / "logs" / self.current_date
        self._ensure_directories()
        
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.zip_dir, self.log_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保目录存在: {directory}")
            
    def _generate_filename(self, prefix: str, extension: str, include_random: bool = True) -> str:
        """生成文件名
        
        Args:
            prefix: 文件名前缀
            extension: 文件扩展名
            include_random: 是否包含随机数
            
        Returns:
            str: 生成的文件名
        """
        if include_random:
            rand_str = str(random.randint(100000, 999999))
            return f"{prefix}_{rand_str}.{extension}"
        return f"{prefix}.{extension}"
        
    def save_to_excel(self, 
                     data_list: List[Dict], 
                     province_name: str, 
                     filename: Optional[str] = None,
                     sheet_name: str = "Sheet1") -> str:
        """
        只保存一组数据到一个Excel文件，不再分组。
        有资费大类且不为'全部'时，文件名拼接该字段，否则不拼接。
        如果所有数据的资费大类都是'全部'，则不写入该列。
        """
        try:
            if not data_list:
                logger.warning("没有数据需要保存")
                return ""
            rand_str = str(random.randint(100000, 999999))
            safe_province = province_name.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
            category = data_list[0].get("资费大类", "全部")
            source = data_list[0].get("资费来源", "未知来源")
            type_text = data_list[0].get("资费类型", "未知类型")
            safe_source = source.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
            safe_type = type_text.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
            # 只在有资费大类且不为"全部"时拼接
            if category and category != "全部":
                safe_category = category.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
                filename = f"{safe_province}_mobile_{rand_str}_{safe_category}_{safe_source}_{safe_type}.xlsx"
            else:
                filename = f"{safe_province}_mobile_{rand_str}_{safe_source}_{safe_type}.xlsx"
                file_path = self.data_dir / filename

            # 检查是否所有数据的"资费大类"都是"全部"或不存在
            if all((item.get("资费大类", "全部") == "全部") for item in data_list):
                # 移除"资费大类"字段
                for item in data_list:
                    item.pop("资费大类", None)

            df = pd.DataFrame(data_list)
            df.to_excel(file_path, sheet_name=sheet_name, index=False)
            logger.debug(f"成功保存 {len(data_list)} 条记录到 {file_path}")
            return str(file_path)
        except Exception as e:
            logger.error(f"保存Excel文件时发生错误: {str(e)}")
            return ""
            
    def make_zip(self, 
                files: List[Union[str, Path]], 
                province_name: str,
                zip_name: Optional[str] = None,
                compression: int = zipfile.ZIP_DEFLATED) -> Optional[str]:
        """将文件列表打包为zip
        
        Args:
            files: 要打包的文件路径列表
            province_name: 省份名称
            zip_name: zip文件名，如果为None则自动生成
            compression: 压缩方法，默认为ZIP_DEFLATED
            
        Returns:
            Optional[str]: zip文件路径，如果打包失败则返回None
        """
        try:
            if not files:
                logger.warning("没有文件需要打包")
                return None

            # 生成zip文件名
            if zip_name is None:
                safe_province = province_name.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
                rand_str = str(random.randint(100000, 999999))
                zip_name = f"{safe_province}_mobile_{rand_str}_资费数据.zip"
            zip_path = self.zip_dir / zip_name

            # 创建zip文件
            with zipfile.ZipFile(zip_path, 'w', compression) as zipf:
                for file_path in files:
                    file_path = Path(file_path)
                    if file_path.exists():
                        # 使用相对路径存储文件
                        arcname = file_path.name
                        zipf.write(file_path, arcname)
                        logger.debug(f"已添加文件到ZIP: {file_path}")
                    else:
                        logger.warning(f"文件不存在，跳过: {file_path}")

            logger.debug(f"已打包为zip: {zip_path}")
            return str(zip_path)

        except Exception as e:
            logger.error(f"创建ZIP文件时出错: {str(e)}")
            return None

# 创建全局数据管理器实例
data_manager = DataManager()

# 更新原有的save_to_excel和make_zip函数以使用DataManager
def save_to_excel(data_list: List[Dict], province_name: str, filename: Optional[str] = None, sheet_name: str = "Sheet1") -> str:
    """
    只保存一组数据到一个Excel文件，不再分组。
    有资费大类且不为'全部'时，文件名拼接该字段，否则不拼接。
    如果所有数据的资费大类都是'全部'，则不写入该列。
    """
    try:
        if not data_list:
            logger.warning("没有数据需要保存")
            return ""
        rand_str = str(random.randint(100000, 999999))
        safe_province = province_name.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
        category = data_list[0].get("资费大类", "全部")
        source = data_list[0].get("资费来源", "未知来源")
        type_text = data_list[0].get("资费类型", "未知类型")
        safe_source = source.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
        safe_type = type_text.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
        # 只在有资费大类且不为"全部"时拼接
        if category and category != "全部":
            safe_category = category.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
            filename = f"{safe_province}_mobile_{rand_str}_{safe_category}_{safe_source}_{safe_type}.xlsx"
        else:
            filename = f"{safe_province}_mobile_{rand_str}_{safe_source}_{safe_type}.xlsx"
        file_path = data_manager.data_dir / filename

        # 检查是否所有数据的"资费大类"都是"全部"或不存在
        if all((item.get("资费大类", "全部") == "全部") for item in data_list):
            # 移除"资费大类"字段
            for item in data_list:
                item.pop("资费大类", None)

        df = pd.DataFrame(data_list)
        df.to_excel(file_path, sheet_name=sheet_name, index=False)
        logger.debug(f"成功保存 {len(data_list)} 条记录到 {file_path}")
        return str(file_path)
    except Exception as e:
        logger.error(f"保存Excel文件时发生错误: {str(e)}")
        return ""

def make_zip(files: List[Union[str, Path]], province_name: str, zip_name: Optional[str] = None) -> Optional[str]:
    """将文件列表打包为zip的兼容函数"""
    return data_manager.make_zip(files, province_name, zip_name)

def setup_driver(options: Optional[ChromeOptions] = None) -> webdriver.Chrome:
    """设置并返回配置好的WebDriver实例
    
    Args:
        options: Chrome浏览器配置选项，如果为None则使用默认配置
        
    Returns:
        webdriver.Chrome: 配置好的WebDriver实例
        
    Raises:
        WebDriverException: 当WebDriver初始化失败时抛出
    """
    try:
        chrome_options = Options()
        options = options or ChromeOptions()
        
        # 基本设置
        if options.headless:
            chrome_options.add_argument('--headless')
        if options.incognito:
            chrome_options.add_argument('--incognito')
        if options.disable_gpu:
            chrome_options.add_argument('--disable-gpu')
        if options.disable_extensions:
            chrome_options.add_argument('--disable-extensions')
        if options.disable_popup_blocking:
            chrome_options.add_argument('--disable-popup-blocking')
        if options.disable_cache:
            chrome_options.add_argument('--disable-application-cache')
            chrome_options.add_argument('--disable-cache')
            chrome_options.add_argument('--disable-offline-load-stale-cache')
            chrome_options.add_argument('--disk-cache-size=0')
            
        # 性能优化设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-logging')
        chrome_options.add_argument('--log-level=3')
        chrome_options.add_argument('--silent')
        
        # 自定义设置
        if options.user_agent:
            chrome_options.add_argument(f'user-agent={options.user_agent}')
        if options.proxy:
            chrome_options.add_argument(f'--proxy-server={options.proxy}')
        if options.window_size:
            chrome_options.add_argument(f'--window-size={options.window_size[0]},{options.window_size[1]}')
        else:
            chrome_options.add_argument('--start-maximized')
            
        # 实验性选项
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 初始化WebDriver
        driver=webdriver.Chrome(options=chrome_options)
            
        # 设置页面加载超时
        driver.set_page_load_timeout(30)
        driver.set_script_timeout(30)
        
        # 清除所有cookies
        driver.delete_all_cookies()
        
        # 注入反检测脚本
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en']
                });
            '''
        })
        
        logger.debug("Chrome WebDriver初始化成功")
        return driver
        
    except Exception as e:
        logger.error(f"设置WebDriver时发生错误: {str(e)}")
        raise

def load_provinces():
    """加载省份信息"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        province_file = os.path.join(current_dir, 'province.json')
        with open(province_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载省份信息失败: {str(e)}")
        return None

def extract_tips_data(tips_items):
    """从tips-item元素中提取数据"""
    tips_data = {}
    for tip in tips_items:
        try:
            attr = tip.find_element(By.CLASS_NAME, "tips-attr").text.strip().split(":")[0]
            content = tip.find_element(By.CLASS_NAME, "tips-content").text.strip()
            tips_data[attr] = content
        except Exception as e:
            logger.error(f"提取提示数据时发生错误: {str(e)}")
    return tips_data

def extract_table_data(table_area):
    """从表格区域提取数据"""
    try:
        # 获取表头行和单元格
        vxe_header_row = table_area.find_element(By.CLASS_NAME, "vxe-header--row.main")
        vxe_cell_titles = vxe_header_row.find_elements(By.CLASS_NAME, "vxe-cell")

        # 获取数据行和单元格
        vxe_body_row = table_area.find_element(By.CLASS_NAME, "vxe-body--row")
        vxe_cell_contents = vxe_body_row.find_elements(By.CLASS_NAME, "vxe-cell")

        # 构建表格数据字典
        table_data = {}
        for title, content in zip(vxe_cell_titles, vxe_cell_contents):
            table_data[title.text.split(":")[0]] = content.text.strip()
        return json.dumps(table_data, ensure_ascii=False)
    except Exception as e:
        logger.error(f"提取表格数据时发生错误: {str(e)}")
        return "{}"

def extract_gray_data(list_gray):
    """从灰色列表区域提取数据"""
    gray_data = {}
    try:
        # 获取所有灰色列表项
        gray_items = list_gray.find_elements(By.CLASS_NAME, "gray-item")
        for gray_item in gray_items:
            try:
                # 提取标题和内容
                gray_title = gray_item.find_element(By.CLASS_NAME, "gray-attr").text.strip().split(":")[0]
                gray_content = gray_item.find_element(By.CLASS_NAME, "gray-content").text.strip()
                gray_data[gray_title] = gray_content
            except Exception as e:
                logger.error(f"提取灰色列表项数据时发生错误: {str(e)}")
    except Exception as e:
        logger.error(f"提取灰色列表数据时发生错误: {str(e)}")
    return gray_data

def process_tariff_container_fast(container, sub_title):
    """超高效处理单个资费容器 - 确保数据完整性"""
    container_data = {}
    start_time = time.time()
    
    try:
        # 快速获取基本信息
        try:
            item_name = container.find_element(By.CLASS_NAME, "name-content").text.strip()
            container_data = {
                "主套餐": sub_title,
                "类别": item_name
            }
        except Exception:
            container_data = {
                "主套餐": sub_title,
                "类别": "未知"
            }
        
        # 使用更全面的DOM查询确保数据完整性
        try:
            # 1. 处理tips数据 - 使用CSS选择器一次性获取
            tips_items = container.find_elements(By.CSS_SELECTOR, ".tips-item")
            for tip in tips_items:
                try:
                    attr_elem = tip.find_element(By.CLASS_NAME, "tips-attr")
                    content_elem = tip.find_element(By.CLASS_NAME, "tips-content")
                    
                    attr_text = attr_elem.text.strip()
                    content_text = content_elem.text.strip()
                    
                    if attr_text and content_text:
                        # 处理属性名，移除冒号和空格
                        key = attr_text.split(':')[0].replace(' ', '').replace('：', '')
                        if key:
                            container_data[key] = content_text
                except Exception:
                    continue
            
            # 2. 处理gray数据 - 使用CSS选择器一次性获取
            gray_items = container.find_elements(By.CSS_SELECTOR, ".gray-item")
            for gray_item in gray_items:
                try:
                    gray_title = gray_item.find_element(By.CLASS_NAME, "gray-attr").text.strip()
                    gray_content = gray_item.find_element(By.CLASS_NAME, "gray-content").text.strip()
                    
                    if gray_title and gray_content:
                        # 处理属性名，移除冒号和空格
                        key = gray_title.split(':')[0].replace(' ', '').replace('：', '')
                        if key:
                            container_data[key] = gray_content
                except Exception:
                    continue
            
            # 3. 处理表格数据 - 使用CSS选择器一次性获取
            row_items = container.find_elements(By.CSS_SELECTOR, ".row-item")
            if row_items:
                table_data = {}
                for row_item in row_items:
                    try:
                        key = row_item.find_element(By.CLASS_NAME, "row-title").text.strip()
                        value = row_item.find_element(By.CLASS_NAME, "row-content").text.strip()
                        if key and value:
                            # 处理属性名，移除冒号和空格
                            clean_key = key.split(':')[0].replace(' ', '').replace('：', '')
                            if clean_key:
                                table_data[clean_key] = value
                    except Exception:
                        continue
                
                if table_data:
                    container_data['表格信息'] = json.dumps(table_data, ensure_ascii=False)
            
            # 4. 处理其他可能的数据结构
            # 查找所有可能包含资费信息的元素
            all_data_elements = container.find_elements(By.CSS_SELECTOR, "[class*='attr'], [class*='content'], [class*='title'], [class*='value']")
            for elem in all_data_elements:
                try:
                    text = elem.text.strip()
                    if text and ':' in text:
                        parts = text.split(':', 1)
                        if len(parts) == 2:
                            key = parts[0].strip().replace(' ', '').replace('：', '')
                            value = parts[1].strip()
                            if key and value and key not in container_data:
                                container_data[key] = value
                except Exception:
                    continue
            
            # 5. 处理特殊字段 - 直接查找关键字段
            special_fields = {
                '资费标准': ['月费', '资费标准', '费用', '价格'],
                '方案编号': ['方案编号', '编号', 'ID'],
                '适用范围': ['适用范围', '适用对象', '用户类型'],
                '适用地区': ['适用地区', '地区', '区域'],
                '销售渠道': ['销售渠道', '渠道', '购买方式'],
                '上线日期': ['上线日期', '开始日期', '生效日期'],
                '下线日期': ['下线日期', '结束日期', '到期日期'],
                '他服务内': ['他服务内', '包含服务', '服务内容', '流量', '通话', '短信'],
                '其他说明': ['其他说明', '备注', '说明']
            }
            
            # 遍历所有文本内容，查找关键字段
            all_text_elements = container.find_elements(By.CSS_SELECTOR, "*")
            for elem in all_text_elements:
                try:
                    text = elem.text.strip()
                    if text and ':' in text:
                        for field_name, keywords in special_fields.items():
                            for keyword in keywords:
                                if keyword in text:
                                    parts = text.split(':', 1)
                                    if len(parts) == 2:
                                        value = parts[1].strip()
                                        if value and field_name not in container_data:
                                            container_data[field_name] = value
                                            break
                except Exception:
                    continue
                        
        except Exception as e:
            # 如果快速方法失败，回退到原始方法确保数据完整性
            logger.debug(f"快速处理失败，回退到原始方法: {str(e)}")
            try:
                # 回退到原始的process_tariff_container方法
                container_data = process_tariff_container(container, sub_title)
            except Exception:
                pass
        
        # 检查处理时间，如果超过限制则记录但不跳过
        processing_time = time.time() - start_time
        if processing_time > MAX_PROCESSING_TIME:
            if ENABLE_MINIMAL_LOGGING:
                logger.warning(f"容器处理时间较长 ({processing_time:.2f}s): {sub_title} - {container_data.get('类别', '')}")
        
        # 确保至少有一些基本数据
        if len(container_data) <= 2:  # 只有主套餐和类别
            logger.warning(f"数据提取不完整，只有 {len(container_data)} 个字段: {sub_title}")
            
    except Exception as e:
        logger.error(f"处理容器时发生错误: {str(e)}")
        
    return container_data

def process_tariff_container(container, sub_title):
    """处理单个资费容器"""
    container_data = {}
    try:
        # 获取基本信息
        item_name = container.find_element(By.CLASS_NAME, "name-content").text.strip()
        container_data = {
            "主套餐": sub_title,
            "类别": item_name
        }
        
        # 一次性获取所有需要的元素，减少DOM查询次数
        tips_list = container.find_element(By.CLASS_NAME, "item-tips-list")
        waterfall_containers = tips_list.find_elements(By.CLASS_NAME, "waterfall-container")
        
        # 使用CSS选择器一次性获取所有tips-item，大幅减少DOM查询
        try:
            # 一次性获取所有tips-item，避免多层嵌套查询
            all_tips_items = container.find_elements(By.CSS_SELECTOR, ".item-tips-list .waterfall-container .waterfall-item .tips-item")
            
            # 批量处理所有tips-item
            for tip in all_tips_items:
                try:
                    # 使用更高效的属性访问
                    attr_elem = tip.find_element(By.CLASS_NAME, "tips-attr")
                    content_elem = tip.find_element(By.CLASS_NAME, "tips-content")
                    
                    # 优化文本处理
                    attr_text = attr_elem.text.strip()
                    content_text = content_elem.text.strip()
                    
                    if attr_text and content_text:
                        # 优化字符串处理
                        attr = attr_text.split(":")[0].replace(" ", "")
                        if attr and content_text:
                            container_data[attr] = content_text
                except Exception:
                    continue
        except Exception:
            pass
        
        # 处理灰色列表数据 - 使用CSS选择器优化
        try:
            gray_items = container.find_elements(By.CSS_SELECTOR, ".list-gray .gray-item")
            
            for gray_item in gray_items:
                try:
                    gray_title = gray_item.find_element(By.CLASS_NAME, "gray-attr").text.strip().split(":")[0]
                    gray_content = gray_item.find_element(By.CLASS_NAME, "gray-content").text.strip()
                    if gray_title and gray_content:
                        container_data[gray_title] = gray_content
                except Exception:
                    continue
        except Exception:
            pass
        
        # 处理表格数据 - 使用CSS选择器优化
        try:
            row_items = container.find_elements(By.CSS_SELECTOR, ".table-area .row-item")
            if row_items:
                table_data = {}
                
                for row_item in row_items:
                    try:
                        key = row_item.find_element(By.CLASS_NAME, "row-title").text.strip()
                        value = row_item.find_element(By.CLASS_NAME, "row-content").text.strip()
                        if key and value:
                            table_data[key] = value
                    except Exception:
                        continue
                
                if table_data:
                    container_data['表格信息'] = json.dumps(table_data, ensure_ascii=False)
        except Exception:
            pass
            
    except Exception:
        pass
    return container_data

def process_single_item(free_cont_item, source_text, type_text):
    """处理单个资费项目 - 使用超高效处理"""
    try:
        free_cont_box = free_cont_item.find_element(By.CLASS_NAME, "free-cont-box")
        sub_title = free_cont_box.find_element(By.CLASS_NAME, "free-cont-subTitle").text.strip()
        
        items_data = []
        # 处理每个资费容器
        tariff_containers = free_cont_box.find_elements(By.CLASS_NAME, "tariff-item-container")
        
        # 使用超高效处理函数
        for i, container in enumerate(tariff_containers):
            # 使用超高效处理函数
            container_data = process_tariff_container_fast(container, sub_title)
            
            # 最小化日志记录
            if ENABLE_MINIMAL_LOGGING:
                total_containers = len(tariff_containers)
                if total_containers > 50 and (i + 1) % 50 == 0:  # 只有超过50个容器且每50个记录一次
                    logger.debug(f"处理进度: {i+1}/{total_containers} - {sub_title}")
                elif total_containers <= 3 and (i + 1) == total_containers:  # 少量容器只在最后一个记录
                    logger.debug(f"处理完成: {sub_title} - {container_data.get('类别', '')}")
            
            if container_data:
                # 添加导航信息到数据中
                container_data["资费来源"] = source_text
                container_data["资费类型"] = type_text
                items_data.append(container_data)
        
        return items_data
    except Exception:
        return []

def process_tariff_items(driver, wait, source_text, type_text, province_id="", province_name=""):
    """多线程处理资费项目"""
    start_time = time.time()  # 添加性能监控
    try:
        # 等待资费元素加载
        tariff_free = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "tariff-free")))
        free_box = tariff_free.find_element(By.CLASS_NAME, "free-box")
        scroll_to_bottom(driver, max_wait_time=10)
        
        # 保存滚动加载完成后的页面HTML
        html_saver.save_selenium_html(
            driver=driver,
            province_code=province_id,
            province_name=province_name,
            operator="mobile",
            page_info=f"滚动加载完成_{source_text}_{type_text}"
        )
        
        free_cont_items = free_box.find_elements(By.CLASS_NAME, "free-cont-item")
        total_items = len(free_cont_items)
        logger.info(f"找到 {total_items} 个资费项目")

        all_items_data = []

        # 优化多线程处理 - 根据项目数量动态调整线程数
        max_workers = min(MAX_WORKERS, max(MIN_WORKERS, total_items // ITEMS_PER_WORKER + 1))
        logger.info(f"使用 {max_workers} 个线程处理 {total_items} 个项目")
        
        # 超高效批量处理 - 使用更大的批次减少线程切换开销
        batch_size = max(5, total_items // max_workers)  # 增加批次大小
        batches = [free_cont_items[i:i + batch_size] for i in range(0, len(free_cont_items), batch_size)]
        
        def safe_process_batch_fast(batch):
            """超高效批量处理一组项目"""
            batch_results = []
            for item in batch:
                try:
                    result = process_single_item(item, source_text, type_text)
                    if result:
                        batch_results.extend(result)
                except Exception as e:
                    if ENABLE_MINIMAL_LOGGING:
                        logger.error(f"处理资费项目时发生错误: {str(e)}")
            return batch_results
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(safe_process_batch_fast, batches))
        
        # 批量收集结果
        for items_data in results:
            if items_data:
                all_items_data.extend(items_data)

        # 优化数据后处理 - 使用更高效的方法
        if all_items_data:
            # 一次性收集所有键并移除空键
            all_keys = set()
            for d in all_items_data:
                # 移除空键
                if "" in d:
                    del d[""]
                # 收集有效键
                all_keys.update(k for k in d.keys() if k and str(k).strip())
            
            # 批量填充缺失键
            for d in all_items_data:
                for key in all_keys:
                    if key not in d:
                        d[key] = ""

        # 性能监控 - 记录处理时间
        processing_time = time.time() - start_time
        avg_time_per_item = processing_time / len(all_items_data) if all_items_data else 0
        
        logger.info(f"成功处理 {len(all_items_data)} 条资费数据，耗时 {processing_time:.2f} 秒")
        logger.info(f"平均每条数据处理时间: {avg_time_per_item:.3f} 秒")
        
        # 性能分析和建议
        if avg_time_per_item > 1.0:
            logger.warning(f"处理速度较慢，建议检查DOM查询效率")
        elif avg_time_per_item < 0.1:
            logger.info(f"处理速度良好")
        
        return all_items_data
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"处理资费项目时发生错误: {str(e)}，耗时 {processing_time:.2f} 秒")
        return []

def find_navigation_boxes(nav_content):
    """查找资费大类、资费来源和资费类型的导航框"""
    nav_boxes = nav_content.find_elements(By.CSS_SELECTOR, "div.nav_box.sortList")
    if not nav_boxes:
        logger.error("未找到导航框元素，请检查页面结构")
        return None, None, None
        
    category_box = None
    source_box = None
    type_box = None
    for box in nav_boxes:
        nav_name = box.find_element(By.CSS_SELECTOR, "span.nav-name").text.strip()
        if "资费大类" in nav_name:
            category_box = box
        elif "资费来源" in nav_name:
            source_box = box
        elif "资费类型" in nav_name:
            type_box = box
    
    if not source_box or not type_box:
        logger.error("未找到资费来源或资费类型的导航框")
        return None, None, None
        
    return category_box, source_box, type_box

def get_navigation_items(box):
    """获取导航框中的所有选项（包括激活和未激活的）"""
    return box.find_elements(By.CSS_SELECTOR, "li.nameActive, li.nav-bar-list")

def scroll_to_bottom(driver, max_wait_time=30):
    """滚动到页面底部并持续滚动，直到没有新内容加载"""
    try:
        start_time = time.time()
        last_height = driver.execute_script("return document.body.scrollHeight")
        no_change_count = 0  # 最大重试次数
        last_item_count = 0  # 记录上一次的元素数量
        
        while time.time() - start_time < max_wait_time:
            # 获取当前可见的资费项目数量
            current_items = len(driver.find_elements(By.CLASS_NAME, "free-cont-item"))
            
            # 滚动到页面底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)  # 等待内容加载
            
            # 获取新的页面高度
            new_height = driver.execute_script("return document.body.scrollHeight")
            
            # 执行上下滚动尝试
            retry_success = False  # 标记是否在重试中成功加载新内容
            consecutive_failures = 0  # 连续失败次数
            
            while consecutive_failures < 3:  # 连续3次失败则退出
                try:
                    # 向上滚动到页面中间
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
                    time.sleep(1)
                    
                    # 再次滚动到底部
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(10)
                    
                    # 检查是否有新内容加载
                    retry_height = driver.execute_script("return document.body.scrollHeight")
                    retry_items = len(driver.find_elements(By.CLASS_NAME, "free-cont-item"))
                    
                    if retry_height > new_height or retry_items > current_items:
                        new_height = retry_height
                        current_items = retry_items
                        no_change_count = 0  # 重置计数器
                        retry_success = True  # 标记重试成功
                        consecutive_failures = 0  # 重置连续失败次数
                    else:
                        consecutive_failures += 1
                except Exception as e:
                    logger.error(f"上下滚动尝试时发生错误: {str(e)}")
                    consecutive_failures += 1
            
            # 检查高度和元素数量的变化
            if new_height == last_height and current_items == last_item_count and not retry_success:
                no_change_count += 1
                if no_change_count >= 3:  # 连续3次没有变化，认为已经到底
                    logger.info("已到达页面底部")
                    break
            else:
                # 如果高度或项目数有变化，或者重试成功，重置计数器
                no_change_count = 0
                last_height = new_height
                last_item_count = current_items
            
        # 最后再滚动回顶部
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
        
        return True
    except Exception as e:
        logger.error(f"滚动页面时发生错误: {str(e)}")
        return False

def display_province_table(provinces):
    """显示省份选择表格"""
    logger.info("省份选择:")
    logger.info("-" * 40)
    logger.info("序号\t省份\tID")
    logger.info("-" * 40)
    
    # 添加数据行
    for i, province in enumerate(provinces, 1):
        logger.info(f"{i}\t{province['text']}\t{province['id']}")
    
    logger.info("-" * 40)
    logger.info("提示：输入 0 爬取所有省份")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="中国移动资费信息爬虫",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 爬取所有省份
  python test1.py --all
  
  # 爬取指定省份
  python test1.py --province 北京 上海 广东
  
  # 爬取指定省份ID
  python test1.py --id 100 200 210
        """
    )
    
    # 添加互斥参数组
    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        "--all",
        action="store_true",
        help="爬取所有省份"
    )
    group.add_argument(
        "--province",
        nargs="+",
        help="指定要爬取的省份名称（可多个）"
    )
    group.add_argument(
        "--id",
        nargs="+",
        help="指定要爬取的省份ID（可多个）"
    )
    
    return parser.parse_args()

def get_provinces_by_names(provinces: List[dict], names: List[str]) -> List[dict]:
    """根据省份名称获取省份信息"""
    result = []
    for name in names:
        found = False
        for province in provinces:
            if province['text'] == name:
                result.append(province)
                found = True
                break
        if not found:
            logger.warning(f"警告：未找到省份 '{name}'")
    return result

def get_provinces_by_ids(provinces: List[dict], ids: List[str]) -> List[dict]:
    """根据省份ID获取省份信息"""
    result = []
    for id in ids:
        found = False
        for province in provinces:
            if province['id'] == id:
                result.append(province)
                found = True
                break
        if not found:
            logger.warning(f"警告：未找到ID为 '{id}' 的省份")
    return result

@contextmanager
def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """重试上下文管理器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔时间（秒）
    """
    retries = 0
    while retries < max_retries:
        try:
            yield
            return  # 成功执行，退出重试
        except Exception as e:
            retries += 1
            if retries == max_retries:
                logger.error(f"达到最大重试次数 {max_retries}，最后一次错误: {str(e)}")
                raise  # 重试次数用完，抛出异常
            logger.warning(f"第 {retries} 次重试，错误: {str(e)}")
            time.sleep(delay * (2 ** (retries - 1)))  # 指数退避

class CallbackManager:
    """回调管理器类，用于处理文件上传和回调"""
    
    def __init__(self, 
                 callback_url: str,
                 max_retries: int = 3,
                 timeout: int = 300):
        """初始化回调管理器
        
        Args:
            callback_url: 回调URL地址
            max_retries: 最大重试次数
            timeout: 超时时间（秒）
        """
        self.callback_url = callback_url
        self.max_retries = max_retries
        self.timeout = timeout
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
            
    async def upload_file(self, 
                         file_path: Union[str, Path],
                         crawler_url: str,
                         status: str = "200") -> Dict[str, Any]:
        """异步上传文件
        
        Args:
            file_path: 要上传的文件路径
            crawler_url: 爬虫URL地址
            status: 爬虫状态，默认为"200"，失败时为"500"
            
        Returns:
            Dict[str, Any]: 回调响应的JSON数据
        """
        file_path = Path(file_path)
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return {"status": "500", "message": f"文件不存在: {file_path}"}
            
        # 检查文件大小
        file_size = file_path.stat().st_size
        if file_size == 0:
            logger.error(f"文件大小为0: {file_path}")
            return {"status": "500", "message": f"文件大小为0: {file_path}"}
            
        logger.info(f"文件大小: {file_size} 字节")
        
        for retry in range(self.max_retries):
            try:
                async with aiofiles.open(file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field(
                        'file',
                        await f.read(),
                        filename=file_path.name,
                        content_type='application/octet-stream'
                    )
                    data.add_field('crawlerUrl', crawler_url)
                    data.add_field('status', status)
                    
                    async with self.session.post(
                        self.callback_url,
                        data=data,
                        ssl=False  # 如果有SSL证书问题，可以禁用SSL验证
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"文件 {file_path} 发送成功")
                            return result
                        else:
                            error_text = await response.text()
                            logger.error(
                                f"文件发送失败，状态码: {response.status}, "
                                f"响应: {error_text}"
                            )
                            if retry < self.max_retries - 1:
                                await asyncio.sleep(2 ** retry)  # 指数退避
                                continue
                            return {
                                "status": "500",
                                "message": f"文件发送失败，状态码: {response.status}, "
                                          f"响应: {error_text}"
                            }
                            
            except Exception as e:
                logger.error(f"发送文件时发生错误: {str(e)}")
                if retry < self.max_retries - 1:
                    await asyncio.sleep(2 ** retry)
                    continue
                return {"status": "500", "message": str(e)}
                
        return {"status": "500", "message": "达到最大重试次数"}

async def send_callback(callback_url: str,
                       zip_path: str,
                       province_id: str,
                       status: str = "200") -> None:
    """发送回调请求
    
    Args:
        callback_url: 回调URL地址
        zip_path: zip文件路径
        province_id: 省份ID
        status: 爬取状态，默认为"200"，失败时为"500"
    """
    try:
        crawler_url = f"https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov={province_id}"
        result = await async_callback(callback_url, zip_path, crawler_url, status)
        logger.debug(f"回调结果: {result}")
    except Exception as e:
        logger.error(f"发送回调时发生错误: {str(e)}")

def group_data_by_category_and_source(data_list):
    grouped = defaultdict(list)
    for item in data_list:
        category = item.get("资费大类", "全部")
        source = item.get("资费来源", "未知来源")
        type_text = item.get("资费类型", "未知类型")
        # 有大类时分组用三元组，无大类时用二元组
        if category and category != "全部":
            key = (category, source, type_text)
        else:
            key = (source, type_text)
        grouped[key].append(item)
    return grouped

def crawl_province(driver: webdriver.Chrome, 
                  wait: WebDriverWait, 
                  province: Dict[str, str]) -> Optional[str]:
    """爬取单个省份的资费信息
    
    Args:
        driver: WebDriver实例
        wait: WebDriverWait实例
        province: 省份信息字典，包含id和text字段
        
    Returns:
        Optional[str]: 生成的zip文件路径，如果失败则返回None
    """
    province_id = province['id']
    province_name = province['text']
    logger.info(f"开始爬取{province_name}(ID: {province_id})的资费信息")

    # ========== 路径生成开始 ==========
    rand_str = str(random.randint(100000, 999999))
    safe_province = province_name.replace('/', '').replace('\\', '').replace(' ', '').replace(':', '')
    zip_dir = data_manager.zip_dir
    zip_name = f"{safe_province}_mobile_{rand_str}_资费数据.zip"
    zip_path = zip_dir / zip_name
    mp4_path = zip_path.with_suffix('.mp4')
    # ========== 路径生成结束 ==========

    # ========== 录屏集成开始 ==========
    screen_recorder = None
    try:
        # 检查是否启用录屏
        if is_screen_recording_enabled():
            # 调整浏览器窗口位置和大小
            window_width, window_height = get_adaptive_window_size()
            driver.set_window_size(window_width, window_height)
            
            # 等待浏览器完全打开并加载页面
            time.sleep(3)  # 等待浏览器稳定
            logger.info("浏览器页面加载完成")
            
            # 调整Anaconda Prompt窗口到右半屏
            anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
            split_window_to_right_half(anaconda_window_titles, show_debug=True)
            
            # 激活并置顶Anaconda Prompt窗口
            activate_window(anaconda_window_titles, show_debug=True)
            
            logger.info("Anaconda Prompt窗口已调整到右半屏并置顶")
            
            screen_recorder = ScreenRecorder(output_file=str(mp4_path), capture_method='dshow')
            screen_recorder.start_recording()
            logger.info(f"录屏已开始，文件: {mp4_path}")
        else:
            logger.info("录屏功能已禁用")
    except Exception as e:
        logger.error(f"录屏启动失败: {str(e)}")
        screen_recorder = None
    # ========== 录屏集成结束 ==========

    try:
        # 访问省份页面
        url = f"https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov={province_id}"
        driver.get(url)
        logger.debug(f"已访问页面: {url}")
        
        # 等待页面加载
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "tariff-free")))
        logger.debug("页面加载完成")
        
        # 保存页面HTML
        html_saver.save_selenium_html(
            driver=driver,
            province_code=province_id,
            province_name=province_name,
            operator="mobile",
            page_info="初始页面"
        )
        
        # 获取导航框
        nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
        category_box, source_box, type_box = find_navigation_boxes(nav_content)
        
        if not source_box or not type_box:
            logger.error("未找到资费来源或资费类型导航框，无法继续爬取")
            return None

        # 存储所有爬取的数据
        all_data = []

        # 检查是否存在资费大类
        has_category = False
        if category_box:
            try:
                # 尝试获取资费大类选项
                category_items = get_navigation_items(category_box)
                if category_items and len(category_items) > 0:
                    has_category = True
                    logger.info(f"找到 {len(category_items)} 个资费大类选项")
                else:
                    logger.info(f"{province_name}没有资费大类选项")
            except Exception as e:
                logger.warning(f"检查资费大类时发生错误: {str(e)}，将按无资费大类处理")

        if has_category:
            # 有资费大类的情况，遍历每个资费大类
            category_items = get_navigation_items(category_box)
            for category_item in category_items:
                try:
                    # 点击资费大类
                    category_text = category_item.text.strip()
                    driver.execute_script("arguments[0].click();", category_item)
                    logger.debug(f"已选择资费大类: {category_text}")
                    time.sleep(2)  # 等待页面加载

                    # 处理当前资费大类下的数据
                    category_data = process_category_data(driver, wait, category_text, source_box, type_box, province_id, province_name)
                    if category_data:
                        all_data.extend(category_data)
                        logger.info(f"成功获取 {len(category_data)} 条{category_text}的资费数据")
                    else:
                        logger.warning(f"未获取到{category_text}的资费数据")

                except Exception as e:
                    logger.error(f"处理资费大类 {category_text} 时发生错误: {str(e)}")
                    continue
        else:
            # 没有资费大类的情况，直接处理资费来源和类型
            logger.info(f"{province_name}没有资费大类，直接处理资费来源和类型")
            category_data = process_category_data(driver, wait, "全部", source_box, type_box, province_id, province_name)
            if category_data:
                all_data.extend(category_data)
                logger.info(f"成功获取 {len(category_data)} 条资费数据")
            else:
                logger.warning(f"未获取到{province_name}的资费数据")

        if not all_data:
            logger.warning(f"未获取到{province_name}的任何资费数据")
            return None

        # === 新增分组保存逻辑 ===
        all_files = []
        grouped = group_data_by_category_and_source(all_data)
        for key, group_items in grouped.items():
            # 判断key类型
            if len(key) == 3:
                # 有大类
                file_path = save_to_excel(group_items, province_name)
            else:
                # 无大类
                file_path = save_to_excel(group_items, province_name)
            all_files.append(file_path)
        # === 结束 ===

        # 创建zip文件，传入提前生成的zip_path
        zip_path_str = data_manager.make_zip(all_files, province_name, zip_name=zip_name)
        if not zip_path_str:
            logger.error("创建zip文件失败")
            return None

        logger.info(f"成功爬取{province_name}的资费信息，共{len(all_data)}条数据")
        return zip_path_str

    except Exception as e:
        logger.error(f"爬取{province_name}时发生错误: {str(e)}")
        return None
    finally:
        # ========== 录屏安全终止 ==========
        if screen_recorder:
            try:
                screen_recorder.stop_recording()
                logger.info(f"录屏已结束，文件: {mp4_path}")
            except Exception as e:
                logger.error(f"录屏结束失败: {str(e)}")
        # ========== 录屏安全终止 ==========

def process_category_data(driver: webdriver.Chrome,
                         wait: WebDriverWait,
                         category_text: str,
                         source_box: Any,
                         type_box: Any,
                         province_id: str = "",
                         province_name: str = "") -> List[Dict]:
    """处理特定资费大类下的数据（或没有资费大类时的数据）
    
    Args:
        driver: WebDriver实例
        wait: WebDriverWait实例
        category_text: 资费大类文本
        source_box: 资费来源导航框
        type_box: 资费类型导航框
        
    Returns:
        List[Dict]: 处理后的数据列表
    """
    category_data = []
    
    try:
        # 重新获取资费来源和资费类型选项
        nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
        _, source_box, type_box = find_navigation_boxes(nav_content)
        if not source_box or not type_box:
            logger.error("重新获取资费来源或资费类型导航框失败")
            return []

        # 获取所有资费来源选项
        source_items = get_navigation_items(source_box)
        if not source_items:
            logger.error("未找到资费来源选项")
            return []

        # 遍历每个资费来源
        for source_item in source_items:
            try:
                # 点击资费来源
                source_text = source_item.text.strip()
                driver.execute_script("arguments[0].click();", source_item)
                logger.debug(f"已选择资费来源: {source_text}")
                time.sleep(2)  # 等待页面加载

                # 重新获取资费类型选项
                nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
                _, _, type_box = find_navigation_boxes(nav_content)
                if not type_box:
                    logger.error("重新获取资费类型导航框失败")
                    continue

                type_items = get_navigation_items(type_box)
                if not type_items:
                    logger.error("重新获取资费类型选项失败")
                    continue

                # 遍历每个资费类型
                for type_item in type_items:
                    try:
                        # 点击资费类型
                        type_text = type_item.text.strip()
                        driver.execute_script("arguments[0].click();", type_item)
                        logger.debug(f"已选择资费类型: {type_text}")
                        time.sleep(2)  # 等待页面加载
                        
                        # # 处理资费项目
                        items_data = process_tariff_items(driver, wait, source_text, type_text, province_id, province_name)
                        if items_data:
                            # 添加资费大类信息到数据中
                            for item in items_data:
                                item["资费大类"] = category_text
                            category_data.extend(items_data)
                            logger.info(f"成功获取 {len(items_data)} 条{category_text}-{source_text}-{type_text}的资费数据")
                        else:
                            logger.warning(f"未获取到{category_text}-{source_text}-{type_text}的资费数据")

                    except Exception as e:
                        logger.error(f"处理资费类型 {type_text} 时发生错误: {str(e)}")
                        # 尝试重新加载页面
                        try:
                            driver.refresh()
                            time.sleep(2)
                            wait.until(EC.presence_of_element_located((By.CLASS_NAME, "tariff-free")))
                            # 重新点击资费来源
                            nav_content = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "nav-bar-content")))
                            _, source_box, _ = find_navigation_boxes(nav_content)
                            if source_box:
                                source_items = get_navigation_items(source_box)
                                for item in source_items:
                                    if item.text.strip() == source_text:
                                        driver.execute_script("arguments[0].click();", item)
                                        time.sleep(2)
                                        break
                        except Exception as refresh_error:
                            logger.error(f"刷新页面时发生错误: {str(refresh_error)}")
                        continue

            except Exception as e:
                logger.error(f"处理资费来源 {source_text} 时发生错误: {str(e)}")
                continue

        return category_data

    except Exception as e:
        logger.error(f"处理资费大类 {category_text} 数据时发生错误: {str(e)}")
        return []

def close_browser(driver: Optional[webdriver.Chrome]) -> None:
    """关闭浏览器
    
    Args:
        driver: WebDriver实例，如果为None则不执行任何操作
    """
    if driver:
        try:
            driver.quit()
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器时发生错误: {str(e)}")

async def main(provinces: Optional[str] = None, callback_url: Optional[str] = None) -> Optional[str]:
    """主函数
    
    Args:
        provinces: 省份编码，单个省份的编码字符串，例如："100"表示北京
        callback_url: 回调URL，用于发送zip文件
        
    Returns:
        Optional[str]: 生成的zip文件路径，如果失败则返回None
    """
    max_retries = 3
    retry_count = 0
    last_error = None
    driver = None
    
    while retry_count < max_retries:
        try:
            # 加载省份信息
            logger.info("正在加载省份信息...")
            all_provinces = load_provinces()
            if not all_provinces:
                logger.error("加载省份信息失败！")
                return None
                
            # 根据传入的provinces参数选择要爬取的省份
            selected_provinces = []
            if provinces:
                # 查找对应的省份信息
                for province in all_provinces:
                    if province['id'] == provinces:
                        selected_provinces.append(province)
                        break
            else:
                # 如果没有指定省份，默认爬取所有省份
                selected_provinces = all_provinces
                
            if not selected_provinces:
                logger.error("没有选择任何省份，程序退出。")
                return None
                
            # 显示选中的省份
            province_info = [f"{p['text']}(ID: {p['id']})" for p in selected_provinces]
            logger.info(f"选中的省份：{', '.join(province_info)}")
            
            # 初始化WebDriver
            logger.info("正在初始化浏览器...")
            driver = setup_driver()
            wait = WebDriverWait(driver, 60)
            
            try:
                # 爬取选中的省份
                total_provinces = len(selected_provinces)
                all_zip_files = []  # 存储所有生成的zip文件路径
                crawl_status = "200"  # 默认爬取状态
                
                # 顺序爬取每个省份
                for province in selected_provinces:
                    province_name = province['text']
                    logger.info(f"正在爬取 {province_name}")
                    
                    try:
                        # 使用重试机制爬取省份数据
                        with retry_on_exception(max_retries=3, delay=2.0):
                            zip_path = crawl_province(driver, wait, province)
                            if zip_path:
                                all_zip_files.append(zip_path)
                            else:
                                raise Exception(f"爬取{province_name}失败，未生成数据文件")
                    except Exception as e:
                        logger.error(f"爬取{province_name}时发生错误: {str(e)}")
                        crawl_status = "500"  # 设置爬取状态为失败
                        raise  # 重新抛出异常，触发外层重试
                    finally:
                        time.sleep(1)  # 添加短暂延迟，避免请求过快
                        
                # 处理回调
                if callback_url and all_zip_files:
                    await send_callback(
                        callback_url,
                        all_zip_files[0],
                        selected_provinces[0]['id'],
                        crawl_status
                    )
                        
                # 成功爬取，关闭浏览器并返回结果
                close_browser(driver)
                return all_zip_files[0] if all_zip_files else None
                
            except Exception as e:
                last_error = e
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"第 {retry_count} 次重试，错误: {str(e)}")
                    time.sleep(2 ** retry_count)  # 指数退避
                    continue
                else:
                    logger.error(f"达到最大重试次数 {max_retries}，最后一次错误: {str(e)}")
                    # 如果发生错误，尝试发送失败状态的回调
                    if callback_url and all_zip_files:
                        await send_callback(
                            callback_url,
                            all_zip_files[0],
                            selected_provinces[0]['id'],
                            "500"
                        )
                    # 3次重试都失败，关闭浏览器
                    close_browser(driver)
                    return None
                    
        except Exception as e:
            last_error = e
            retry_count += 1
            if retry_count < max_retries:
                logger.warning(f"第 {retry_count} 次重试，错误: {str(e)}")
                time.sleep(2 ** retry_count)  # 指数退避
                continue
            else:
                logger.error(f"达到最大重试次数 {max_retries}，最后一次错误: {str(e)}")
                # 3次重试都失败，关闭浏览器
                close_browser(driver)
                return None
                
    logger.info(f"爬取完成！共爬取 {len(selected_provinces)} 个省份的数据")
    return None

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_args()
    # 设置要爬取的省份
    provinces_to_crawl = None
    if args.all:
        provinces_to_crawl = None  # 爬取所有省份
    elif args.province:
        provinces_to_crawl = args.province[0]  # 暂时只支持爬取第一个省份
    elif args.id:
        provinces_to_crawl = args.id[0]  # 暂时只支持爬取第一个省份ID
        
    # 运行主函数
    try:
        zip_path = asyncio.run(main(provinces_to_crawl))
        if zip_path:
            logger.info(f"爬取完成，数据已保存到: {zip_path}")
        else:
            logger.error("爬取失败，未生成数据文件")
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
