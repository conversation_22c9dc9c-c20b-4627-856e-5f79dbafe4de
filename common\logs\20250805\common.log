2025-08-05 15:16:51 - MainProcess - 29608 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:16:52 - SpawnProcess-1 - 27208 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:16:55 - MainProcess - 52708 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (32, 32)
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-05 15:17:07 - MainProcess - 52708 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-05 15:17:07 - MainProcess - 52708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 15:17:07 - MainProcess - 52708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-05 15:17:15 - MainProcess - 52708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 15:17:15 - MainProcess - 52708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-05 15:21:24 - MainProcess - 52708 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 108450 字节
2025-08-05 15:21:24 - MainProcess - 52708 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-05 15:35:41 - MainProcess - 47448 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:35:42 - SpawnProcess-1 - 22312 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:35:52 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-05 15:35:52 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - INFO - [screen_record.py:39] - 录屏采集方式: dshow
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - INFO - [screen_record.py:43] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-05 15:35:56 - SpawnProcess-1 - 22312 - common - INFO - [screen_record.py:53] - 开始录屏...
2025-08-05 15:57:05 - MainProcess - 16964 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 15:57:07 - SpawnProcess-1 - 33140 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 20:29:40 - MainProcess - 29420 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 20:29:42 - SpawnProcess-1 - 47592 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250805
2025-08-05 20:29:47 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-05 20:29:47 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (96, 96)
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-05 20:29:50 - SpawnProcess-1 - 47592 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
