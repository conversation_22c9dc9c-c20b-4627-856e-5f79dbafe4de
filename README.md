# 运营商资费数据采集系统

## 项目简介
本项目是一个自动化采集各运营商资费数据的系统，支持中国移动、中国电信、中国联通和中国广电的资费数据采集。系统通过 API 接口提供服务，可以异步执行数据采集任务，并通过回调接口返回采集结果。

## 功能特点
- 支持多运营商数据采集（移动、电信、联通、广电）
- 提供统一的 RESTful API 接口
- 异步任务处理
- 自动生成数据文件和压缩包
- 支持回调通知
- 详细的日志记录
- 完善的错误处理机制

## 技术栈
- Python 3.x
- FastAPI
- Selenium
- ChromeDriver
- uvicorn
- aiohttp
- logging

## 目录结构
```
.
├── api/
│   └── main.py                 # API 服务入口
├── ChinaMobile/               # 中国移动爬虫
│   ├── mobile_crawler.py
│   ├── data/                  # 数据存储目录
│   └── zip/                   # 压缩包存储目录
├── ChinaTelecom/             # 中国电信爬虫
│   ├── telecom_crawler.py
│   ├── data/
│   └── zip/
├── ChinaUnicom/              # 中国联通爬虫
│   ├── unicom_crawler.py
│   ├── data/
│   └── zip/
├── ChinaBroadnet/            # 中国广电爬虫
│   ├── broadnet_crawler.py
│   ├── data/
│   └── zip/
├── common/
│   └── logger_config.py      # 日志配置
└── README.md
```

## 安装说明

1. 克隆项目
```bash
git clone [项目地址]
cd [项目目录]
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 安装 Chrome 浏览器和 ChromeDriver
- 确保已安装 Chrome 浏览器
- 下载与 Chrome 版本匹配的 ChromeDriver
- 将 ChromeDriver 添加到系统环境变量

## 使用说明

### 启动服务
```bash
cd api
python main.py
```
服务将在 http://localhost:8123 启动

### API 接口说明

#### 1. 启动爬虫任务
```
POST /api/v1/crawl
```

请求体：
```json
{
    "provinces": "省份编码",
    "callback_url": "回调URL",
    "crawler_type": "运营商类型"
}
```

参数说明：
- provinces: 省份编码（部分运营商需要）
- callback_url: 回调通知URL
- crawler_type: 运营商类型（mobile/telecom/unicom/broadnet）

响应：
```json
{
    "task_id": "任务ID",
    "status": "accepted",
    "message": "任务已接受"
}
```

#### 2. 查询任务状态
```
GET /api/v1/task/{task_id}
```

响应：
```json
{
    "task_id": "任务ID",
    "status": "任务状态",
    "message": "状态信息"
}
```

### 回调通知
当数据采集完成后，系统会向指定的回调URL发送POST请求，请求体包含：
- file: 采集数据的zip文件
- crawlerUrl: 采集源URL

## 数据存储
- 每个运营商的爬虫都会在各自的目录下创建 data 和 zip 文件夹
- data 文件夹存储原始JSON数据
- zip 文件夹存储打包后的数据文件

## 日志说明
- 日志文件保存在各运营商目录的 logs 文件夹下
- 日志格式：`telecom_YYYYMMDD.log`
- 保留最近5个日志文件

## 注意事项
1. 确保 Chrome 浏览器和 ChromeDriver 版本匹配
2. 建议使用虚拟环境运行项目
3. 需要稳定的网络连接
4. 建议定期清理数据文件和日志文件

## 错误处理
- 所有错误都会被记录到日志文件中
- API 接口会返回适当的 HTTP 状态码和错误信息
- 爬虫任务失败会通过回调接口通知

## 开发说明
1. 遵循 PEP 8 编码规范
2. 所有函数必须有文档字符串
3. 使用 logging 模块记录日志
4. 实现完善的错误处理机制

## 许可证
[许可证类型]

## 联系方式
[联系方式] 