from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_shandong_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析山东资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析山东{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.list-box", timeout=10000)
        
        # 存储所有产品数据
        all_products = []
        
        # 获取所有产品项
        products = await page.query_selector_all("div.list-box > div")
        
        if not products:
            logger.warning("未找到产品项")
            return
            
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.top.clearfix > p.fl.title")
                code_elem = await product.query_selector("div.top.clearfix > p.fr.small")
                
                if not title_elem or not code_elem:
                    logger.error(f"第 {index} 个产品未找到标题或编号元素")
                    continue
                    
                title = await title_elem.text_content()
                code = await code_elem.text_content()
                
                # 提取方案编号
                if "方案编号：" in code:
                    code = code.split("方案编号：")[1].strip()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"产品编号: {code}")
                
                # 获取基本信息
                basic_info = {}
                info_div = await product.query_selector("div.table > div.text > div.clearfix")
                if info_div:
                    # 获取左侧信息
                    left_div = await info_div.query_selector("div:first-child")
                    if left_div:
                        left_items = await left_div.query_selector_all("p")
                        for item in left_items:
                            text = await item.text_content()
                            if "：" in text:
                                key, value = text.split("：", 1)
                                basic_info[key.strip()] = value.strip()
                    
                    # 获取右侧信息
                    right_div = await info_div.query_selector("div:last-child")
                    if right_div:
                        right_items = await right_div.query_selector_all("p")
                        for item in right_items:
                            text = await item.text_content()
                            if "：" in text:
                                key, value = text.split("：", 1)
                                basic_info[key.strip()] = value.strip()
                
                logger.info(f"基本信息: {basic_info}")
                
                # 获取服务内容
                service_content = {}
                service_table = await product.query_selector("div.table-out > table")
                if service_table:
                    headers = await service_table.query_selector_all("th")
                    values = await service_table.query_selector_all("td")
                    
                    for i in range(min(len(headers), len(values))):
                        header = await headers[i].text_content()
                        value = await values[i].text_content()
                        if header and value and header != "-" and value != "-":
                            service_content[header.strip()] = value.strip()
                
                logger.info(f"服务内容: {service_content}")
                
                # 获取备注信息
                remark = ""
                remark_div = await product.query_selector("div.remark")
                if remark_div:
                    # 获取所有备注段落
                    remark_paragraphs = await remark_div.query_selector_all("p")
                    remark_texts = []
                    for p in remark_paragraphs:
                        text = await p.text_content()
                        if text.strip():
                            remark_texts.append(text.strip())
                    remark = "\n".join(remark_texts)
                    logger.info(f"备注信息: {remark[:100]}...")  # 只记录前100个字符
                else:
                    logger.warning(f"第 {index} 个产品未找到备注信息")
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                all_products.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(all_products)}个")
        
        if all_products:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = all_products
            logger.info(f"成功保存{menu_type}的{len(all_products)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_shandong_menu_config() -> dict:
    """获取山东的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.menu-box",
        'menu_selector': "div.top.clearfix > div",
        'list_selector': "div.list-box"
    } 