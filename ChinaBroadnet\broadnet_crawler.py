"""
中国广电资费数据爬虫

此模块用于自动化爬取中国广电网站的资费数据，包括套餐信息、基础业务、其它业务等详细信息。
使用Selenium进行网页自动化操作，支持多级下拉菜单选择和数据提取。
"""

import os
import sys
import requests
import asyncio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import time
import random
import json
import zipfile
from datetime import datetime
from common.logger_config import broadnet_logger as logger
# 导入公共回调方法
from common.callback_utils import async_callback
# 导入HTML保存工具
from common.html_saver import html_saver
# 导入配置加载工具
from common.config_loader import is_screen_recording_enabled
# 导入ScreenRecorder
from common.screen_record import ScreenRecorder
# 导入窗口操作工具
from common.window_utils import split_window_to_right_half, move_window_to_bottom_right, get_adaptive_window_size, activate_window

# ==================== Browser Setup and Configuration ====================

def setup_driver():
    """
    初始化并配置Chrome浏览器驱动
    
    Returns:
        webdriver.Chrome: 配置好的Chrome WebDriver实例
        
    Raises:
        WebDriverException: 当WebDriver初始化失败时
        Exception: 当浏览器初始化过程中出现未知错误时
    """
    logger.info("正在初始化Chrome浏览器...")
    try:
        chrome_options = Options()
        
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # 随机选择用户代理
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
        ]
        chrome_options.add_argument(f'user-agent={random.choice(user_agents)}')
        
        logger.info("正在创建Chrome WebDriver实例...")
        driver = webdriver.Chrome(options=chrome_options)
        logger.info("Chrome WebDriver实例创建成功")
        
        driver.set_page_load_timeout(30)
        logger.info("页面加载超时时间设置为30秒")
        
        logger.info("Chrome浏览器初始化完成")
        return driver
    except WebDriverException as e:
        logger.error(f"WebDriver初始化失败: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"浏览器初始化过程中出现未知错误: {str(e)}")
        raise

# ==================== Utility Functions ====================

def random_sleep(min_seconds=2, max_seconds=5):
    """
    随机等待一段时间，模拟人工操作
    
    Args:
        min_seconds (float): 最小等待时间（秒）
        max_seconds (float): 最大等待时间（秒）
    """
    sleep_time = random.uniform(min_seconds, max_seconds)
    logger.debug(f"随机等待 {sleep_time:.2f} 秒")
    time.sleep(sleep_time)

def wait_for_element(driver, by, value, timeout=20):
    """
    等待元素出现，带超时处理
    
    Args:
        driver: WebDriver实例
        by: 定位方式
        value: 定位值
        timeout (int): 超时时间（秒）
        
    Returns:
        WebElement: 找到的元素，如果未找到则返回None
    """
    try:
        logger.debug(f"等待元素出现: {by}={value}, 超时时间={timeout}秒")
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        logger.debug(f"元素已找到: {by}={value}")
        return element
    except TimeoutException:
        logger.error(f"等待元素超时: {by}={value}")
        return None
    except Exception as e:
        logger.error(f"等待元素时出现错误: {str(e)}")
        return None

def wait_for_page_load(driver, timeout=10):
    """
    等待页面加载完成
    
    Args:
        driver: WebDriver实例
        timeout (int): 超时时间（秒）
        
    Returns:
        bool: 页面是否加载完成
    """
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
        return True
    except Exception as e:
        logger.error(f"等待页面加载超时: {str(e)}")
        return False

def scroll_to_bottom(driver):
    """
    滚动到页面底部
    
    Args:
        driver: WebDriver实例
    """
    try:
        logger.info("正在滚动到页面底部...")
        page_height = driver.execute_script("return document.body.scrollHeight")
        logger.debug(f"页面总高度: {page_height}px")
        
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        logger.info("已滚动到页面底部")
        
        random_sleep(1, 2)
    except Exception as e:
        logger.error(f"滚动页面时出现错误: {str(e)}")

# ==================== Data Extraction Functions ====================

def extract_tariff_data(driver):
    """
    从页面元素中提取资费数据
    
    Args:
        driver: WebDriver实例
        
    Returns:
        list: 提取的资费数据列表，如果提取失败则返回None
    """
    try:
        logger.info("开始提取资费数据...")
        
        sec_box_selectors = [
            (By.CLASS_NAME, "secBox"),
            (By.XPATH, "//section[contains(@class, 'secBox')]"),
            (By.XPATH, "//div[contains(@class, 'secBox')]"),
            (By.XPATH, "//div[contains(@class, 'el-table')]"),
            (By.XPATH, "//div[contains(@class, 'tabelBox')]"),
            (By.XPATH, "//div[contains(@class, 'til')]/parent::section"),
            (By.XPATH, "//div[contains(@class, 'titleDetail')]/parent::section")
        ]
        
        all_tariff_data = []
        
        for by, selector in sec_box_selectors:
            try:
                logger.debug(f"尝试定位资费信息区域: {selector}")
                sec_boxes = driver.find_elements(by, selector)
                if sec_boxes:
                    logger.info(f"找到 {len(sec_boxes)} 个资费信息区域")
                    
                    for i, sec_box in enumerate(sec_boxes):
                        try:
                            # 提取套餐名称
                            try:
                                title = sec_box.find_element(By.CLASS_NAME, "til").text.strip()
                            except:
                                title = sec_box.find_element(By.XPATH, ".//div[contains(@class, 'til')]").text.strip()
                            logger.debug(f"处理第 {i+1} 个套餐: {title}")

                            tariff_data = {
                                "套餐名称": title,
                                "基础业务": {},
                                "其它业务": {},
                                "详细信息": {}
                            }

                            try:
                                # 检查是否存在tab页
                                has_tabs = sec_box.find_element(By.XPATH, ".//div[contains(@class, 'tabCont')]")
                            except:
                                has_tabs = False
                                logger.debug("未找到tab页，将直接提取表格数据")

                            if has_tabs:
                                _process_tabbed_data(driver, sec_box, tariff_data)
                            else:
                                _process_untabbed_data(driver, sec_box, tariff_data)

                            # 提取详情信息
                            _extract_detail_info(sec_box, tariff_data)
                            
                            all_tariff_data.append(tariff_data)
                            logger.info(f"成功提取第 {i+1} 个套餐 {title} 的数据")
                            
                        except Exception as e:
                            logger.error(f"处理单个套餐数据时出错: {str(e)}")
                            continue
                    
                    if all_tariff_data:
                        break
                        
            except Exception as e:
                logger.debug(f"使用选择器 {selector} 未找到元素: {str(e)}")
                continue

        if not all_tariff_data:
            raise Exception("未找到任何资费信息")

        return all_tariff_data

    except Exception as e:
        logger.error(f"提取资费数据时出错: {str(e)}")
        return None

def _process_tabbed_data(driver, sec_box, tariff_data):
    """处理带标签页的数据"""
    try:
        # 处理基础业务页签
        _process_basic_business_tab(driver, sec_box, tariff_data)
        
        # 处理其它业务页签
        _process_other_business_tab(driver, sec_box, tariff_data)
    except Exception as e:
        logger.error(f"处理标签页数据时出错: {str(e)}")

def _process_basic_business_tab(driver, sec_box, tariff_data):
    """
    处理基础业务页签
    
    Args:
        driver: WebDriver实例
        sec_box: 包含数据的section元素
        tariff_data: 存储数据的字典
    """
    try:
        # 点击基础业务页签
        basic_tab = sec_box.find_element(By.XPATH, ".//div[contains(@class, 'tabCont') and contains(text(), '基础业务')]")
        driver.execute_script("arguments[0].click();", basic_tab)
        
        # 获取表头字段名
        header_cells = sec_box.find_elements(By.XPATH, ".//th[contains(@class, 'headCell')]//div[contains(@class, 'cell')]")
        field_names = []
        for header in header_cells:
            # 合并多行表头文本
            header_text = ' '.join([text.strip() for text in header.text.split('\n') if text.strip()])
            field_names.append(header_text)
        
        # 提取基础业务表格数据
        rows = sec_box.find_elements(By.CLASS_NAME, "el-table__row")
        if not rows:
            rows = sec_box.find_elements(By.XPATH, ".//tr[contains(@class, 'el-table__row')]")
        
        # 存储所有行的数据
        all_rows_data = []
        
        # 用于跟踪跨行单元格的值
        rowspan_values = {}
        
        for row_idx, row in enumerate(rows):
            cells = row.find_elements(By.CLASS_NAME, "cellName")
            if not cells:
                cells = row.find_elements(By.XPATH, ".//td[contains(@class, 'cellName')]")
            
            if cells:
                row_data = {}
                # 首先处理所有字段，确保每个字段都有值
                for col_idx, field_name in enumerate(field_names):
                    field_key = field_name.replace(" ","")
                    # 检查是否有跨行单元格的值需要继承
                    if col_idx in rowspan_values and rowspan_values[col_idx]['remaining'] > 0:
                        row_data[field_key] = rowspan_values[col_idx]['value']
                        rowspan_values[col_idx]['remaining'] -= 1
                    else:
                        # 如果没有跨行值，则使用当前单元格的值
                        if col_idx < len(cells):
                            cell = cells[col_idx]
                            rowspan = cell.get_attribute("rowspan")
                            if rowspan:
                                rowspan = int(rowspan)
                                # 存储跨行单元格的值
                                rowspan_values[col_idx] = {
                                    'value': cell.text.strip(),
                                    'remaining': rowspan - 1
                                }
                            row_data[field_key] = cell.text.strip()
                        else:
                            row_data[field_key] = ""
                
                all_rows_data.append(row_data)
        
        if all_rows_data:
            tariff_data["基础业务"] = all_rows_data
            logger.info(f"成功提取 {len(all_rows_data)} 行基础业务数据")
            
    except Exception as e:
        logger.error(f"处理基础业务数据时出错: {str(e)}")

def _process_other_business_tab(driver, sec_box, tariff_data):
    """
    处理其它业务页签
    
    Args:
        driver: WebDriver实例
        sec_box: 包含数据的section元素
        tariff_data: 存储数据的字典
    """
    try:
        # 点击其它业务页签
        other_tab = sec_box.find_element(By.XPATH, ".//div[contains(@class, 'tabCont') and contains(text(), '其它业务')]")
        driver.execute_script("arguments[0].click();", other_tab)
        
        # 获取表头字段名
        header_cells = sec_box.find_elements(By.XPATH, ".//th[contains(@class, 'headCell')]//div[contains(@class, 'cell')]")
        field_names = []
        for header in header_cells:
            # 合并多行表头文本
            header_text = ' '.join([text.strip() for text in header.text.split('\n') if text.strip()])
            field_names.append(header_text)
        
        # 提取其它业务表格数据
        rows = sec_box.find_elements(By.CLASS_NAME, "el-table__row")
        if not rows:
            rows = sec_box.find_elements(By.XPATH, ".//tr[contains(@class, 'el-table__row')]")
        
        # 存储所有行的数据
        all_rows_data = []
        
        # 用于跟踪跨行单元格的值
        rowspan_values = {}
        
        for row_idx, row in enumerate(rows):
            cells = row.find_elements(By.CLASS_NAME, "cellName")
            if not cells:
                cells = row.find_elements(By.XPATH, ".//td[contains(@class, 'cellName')]")
            
            if cells:
                row_data = {}
                # 首先处理所有字段，确保每个字段都有值
                for col_idx, field_name in enumerate(field_names):
                    field_key = field_name.replace(" ","")
                    # 检查是否有跨行单元格的值需要继承
                    if col_idx in rowspan_values and rowspan_values[col_idx]['remaining'] > 0:
                        row_data[field_key] = rowspan_values[col_idx]['value']
                        rowspan_values[col_idx]['remaining'] -= 1
                    else:
                        # 如果没有跨行值，则使用当前单元格的值
                        if col_idx < len(cells):
                            cell = cells[col_idx]
                            rowspan = cell.get_attribute("rowspan")
                            if rowspan:
                                rowspan = int(rowspan)
                                # 存储跨行单元格的值
                                rowspan_values[col_idx] = {
                                    'value': cell.text.strip(),
                                    'remaining': rowspan - 1
                                }
                            row_data[field_key] = cell.text.strip()
                        else:
                            row_data[field_key] = ""
                
                all_rows_data.append(row_data)
        
        if all_rows_data:
            tariff_data["其它业务"] = all_rows_data
            logger.info(f"成功提取 {len(all_rows_data)} 行其它业务数据")
            
    except Exception as e:
        logger.error(f"处理其它业务数据时出错: {str(e)}")

def _process_untabbed_data(driver, sec_box, tariff_data):
    """处理无标签页的数据"""
    try:
        # 获取表头字段名
        header_cells = sec_box.find_elements(By.XPATH, ".//th[contains(@class, 'headCell')]//div[contains(@class, 'cell')]")
        field_names = []
        for header in header_cells:
            header_text = ' '.join([text.strip() for text in header.text.split('\n') if text.strip()])
            field_names.append(header_text)
        
        # 提取表格数据
        rows = sec_box.find_elements(By.CLASS_NAME, "el-table__row")
        if not rows:
            rows = sec_box.find_elements(By.XPATH, ".//tr[contains(@class, 'el-table__row')]")
        
        table_data = []
        for row in rows:
            cells = row.find_elements(By.CLASS_NAME, "cellName")
            if not cells:
                cells = row.find_elements(By.XPATH, ".//td[contains(@class, 'cellName')]")
            
            if len(cells) >= len(field_names):
                row_data = {
                    field_names[i].replace(" ",""): cells[i].text.strip()
                    for i in range(len(field_names))
                }
                table_data.append(row_data)
        
        if table_data:
            tariff_data["基础业务"] = table_data
    except Exception as e:
        logger.error(f"处理无标签页数据时出错: {str(e)}")

def _extract_detail_info(sec_box, tariff_data):
    """提取详情信息"""
    try:
        detail_sections = sec_box.find_elements(By.CLASS_NAME, "deatil")
        if not detail_sections:
            detail_sections = sec_box.find_elements(By.XPATH, ".//div[contains(@class, 'deatil')]")
        
        if detail_sections:
            detail_items = detail_sections[0].find_elements(By.TAG_NAME, "div")
            for item in detail_items:
                try:
                    label = item.find_element(By.CLASS_NAME, "lef").text.strip().rstrip("：")
                    value = item.text.replace(label + "：", "").strip()
                    tariff_data["详细信息"][label] = value
                except:
                    continue
    except Exception as e:
        logger.error(f"提取详情信息时出错: {str(e)}")

# ==================== Data Storage Functions ====================

def ensure_directories():
    """
    确保必要的目录结构存在
    """
    try:
        # 获取当前文件所在目录的绝对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 在当前目录下创建data和zip目录
        data_dir = os.path.join(current_dir, "data")
        zip_dir = os.path.join(current_dir, "zip")
        
        # 创建目录
        for dir_path in [data_dir, zip_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"创建目录: {dir_path}")
        
        return current_dir
    except Exception as e:
        logger.error(f"创建目录结构时出错: {str(e)}")
        raise

def save_data_to_files(province_name, tab_name, data):
    """
    保存数据到文件
    
    Args:
        province_name (str): 省份名称
        tab_name (str): 选项卡名称
        data (dict): 要保存的数据
        
    Returns:
        str: 保存的文件路径，如果保存失败则返回None
    """
    try:
        # 获取基础目录
        base_dir = ensure_directories()
        date_str = datetime.now().strftime("%Y%m%d")
        data_dir = os.path.join(base_dir, "data", date_str)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"创建日期子目录: {data_dir}")
        
        # 生成随机数
        random_num = random.randint(1000, 9999)
        
        # 生成文件名
        json_filename = f"{province_name}_broadnet_{date_str}_{random_num}_{tab_name}.json"
        json_filepath = os.path.join(data_dir, json_filename)
        
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到文件: {json_filepath}")
        
        return json_filepath
    except Exception as e:
        logger.error(f"保存数据到文件时出错: {str(e)}")
        return None

def create_province_zip(province_name, json_files, zip_file_path):
    """
    为特定省份创建ZIP压缩包
    Args:
        province_name (str): 省份名称
        json_files (list): JSON文件路径列表
        zip_file_path (str): 统一的zip文件完整路径
    Returns:
        str: ZIP文件完整路径，如果创建失败则返回None
    """
    try:
        with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for json_file in json_files:
                if os.path.exists(json_file):
                    file_name = os.path.basename(json_file)
                    zipf.write(json_file, file_name)
                    logger.info(f"已添加文件到ZIP: {json_file}")
        logger.info(f"ZIP压缩包创建成功: {zip_file_path}")
        return zip_file_path
    except Exception as e:
        logger.error(f"创建ZIP压缩包时出错: {str(e)}")
        return None

# def get_latest_zip_file():
#     """
#     获取最新生成的zip文件路径
    
#     Returns:
#         str: zip文件路径，如果未找到则返回None
#     """
#     try:
#         # 获取基础目录
#         base_dir = ensure_directories()
#         zip_dir = os.path.join(base_dir, "zip")
        
#         zip_files = [f for f in os.listdir(zip_dir) if f.endswith('.zip')]
#         if zip_files:
#             latest_zip = max(zip_files, key=lambda x: os.path.getctime(os.path.join(zip_dir, x)))
#             zip_file_path = os.path.join(zip_dir, latest_zip)
#             logger.info(f"找到最新生成的zip文件: {zip_file_path}")
#             return zip_file_path
#         else:
#             logger.error("未找到生成的zip文件")
#             return None
#     except Exception as e:
#         logger.error(f"获取最新zip文件时出错: {str(e)}")
#         return None

# ==================== Dropdown Menu Handling ====================

def handle_dropdown_menu(driver, dropdown_element):
    """
    处理下拉菜单的交互
    
    Args:
        driver: WebDriver实例
        dropdown_element: 下拉框元素
        
    Returns:
        tuple: (下拉菜单元素, 下拉选项列表)，如果处理失败则返回(None, None)
    """
    try:
        driver.execute_script("arguments[0].click();", dropdown_element)
        random_sleep(1, 2)
        
        dropdown_menu = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "el-select-dropdown__list"))
        )
        
        dropdown_options = dropdown_menu.find_elements(By.TAG_NAME, "li")
        if not dropdown_options:
            logger.error("未找到下拉选项")
            return None, None
            
        logger.info(f"找到 {len(dropdown_options)} 个下拉选项")
        return dropdown_menu, dropdown_options
        
    except Exception as e:
        logger.error(f"处理下拉菜单时出错: {str(e)}")
        return None, None

def ensure_dropdown_visible(driver, dropdown_element, dropdown_menu):
    """
    确保下拉菜单可见
    
    Args:
        driver: WebDriver实例
        dropdown_element: 下拉框元素
        dropdown_menu: 下拉菜单元素
        
    Returns:
        tuple: (下拉菜单元素, 下拉选项列表)，如果处理失败则返回(None, None)
    """
    try:
        if not dropdown_menu.is_displayed():
            driver.execute_script("arguments[0].click();", dropdown_element)
            random_sleep(1, 2)
            dropdown_menu = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "el-select-dropdown__list"))
            )
            dropdown_options = dropdown_menu.find_elements(By.TAG_NAME, "li")
            if not dropdown_options:
                logger.error("重新打开下拉菜单后未找到选项")
                return None, None
            return dropdown_menu, dropdown_options
        return dropdown_menu, dropdown_menu.find_elements(By.TAG_NAME, "li")
    except Exception as e:
        logger.error(f"确保下拉菜单可见时出错: {str(e)}")
        return None, None

# ==================== Tab Processing Functions ====================

async def process_tab_data(driver, tab, tab_name, province_code="", province_name=""):
    """
    处理单个选项卡的数据
    
    Args:
        driver: WebDriver实例
        tab: 选项卡元素
        tab_name: 选项卡名称
        
    Returns:
        dict: 处理后的数据，如果处理失败则返回None
    """
    try:
        driver.execute_script("arguments[0].click();", tab)
        random_sleep(2, 3)
        
        if not wait_for_page_load(driver):
            return None
            
        tab_data = {}
        
        # 处理第一个下拉框
        first_dropdown = wait_for_element(driver, By.XPATH, "//div[contains(@class, 'left')]//div[contains(@class, 'el-select')]//input")
        if not first_dropdown:
            logger.error("未找到第一个下拉框")
            return None
            
        dropdown_menu, dropdown_options = handle_dropdown_menu(driver, first_dropdown)
        if not dropdown_menu or not dropdown_options:
            return None
            
        for first_option in dropdown_options:
            try:
                dropdown_menu, current_options = ensure_dropdown_visible(driver, first_dropdown, dropdown_menu)
                if not dropdown_menu or not current_options:
                    continue
                
                first_option_text = first_option.text.strip()
                logger.info(f"选择第一个选项: {first_option_text}")
                driver.execute_script("arguments[0].click();", first_option)
                random_sleep(4, 6)  # 增加第一个选项选择后的等待时间
                
                # 等待页面加载完成
                logger.info("等待页面加载完成...")
                if not wait_for_page_load(driver, timeout=15):
                    logger.warning("页面加载超时，继续处理下一个选项")
                    continue
                
                # 额外等待确保第二个下拉框加载完成
                logger.info("等待第二个下拉框加载...")
                random_sleep(3, 5)
                
                # 处理第二个下拉框
                second_dropdown_data = await _handle_second_dropdown(driver, first_option_text, province_code=province_code, province_name=province_name, tab_name=tab_name)
                if second_dropdown_data:
                    tab_data.update(second_dropdown_data)
                
            except Exception as e:
                logger.error(f"处理第一个下拉选项时出错: {str(e)}")
                continue
        
        return tab_data
        
    except Exception as e:
        logger.error(f"处理选项卡数据时出错: {str(e)}")
        return None

async def _handle_second_dropdown(driver, first_option_text, province_code="", province_name="", tab_name=""):
    """
    处理第二个下拉框
    
    Args:
        driver: WebDriver实例
        first_option_text: 第一个选项的文本（选项卡名称：全网资费或省份资费）
        province_code: 省份编码
        province_name: 省份名称
        tab_name: tab名称
        
    Returns:
        dict: 处理后的数据，如果处理失败则返回None
    """
    try:
        # 等待第二个下拉框出现
        logger.info("等待第二个下拉框出现...")
        second_dropdown = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'left')]//div[contains(@class, 'el-select')][2]//div[contains(@class, 'el-input')]"))
        )
        WebDriverWait(driver, 30).until(
            EC.visibility_of_element_located((By.XPATH, "//div[contains(@class, 'left')]//div[contains(@class, 'el-select')][2]//div[contains(@class, 'el-input')]"))
        )
        
        # 点击第二个下拉框
        logger.info("点击第二个下拉框...")
        driver.execute_script("arguments[0].scrollIntoView(true);", second_dropdown)
        random_sleep(2, 3)  # 增加等待时间
        
        actions = ActionChains(driver)
        actions.move_to_element(second_dropdown)
        actions.click()
        actions.perform()
        random_sleep(3, 5)  # 增加点击后的等待时间
        
        # 等待下拉菜单出现
        logger.info("等待下拉菜单出现...")
        second_dropdown_menu = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]"))
        )
        
        # 获取选项
        logger.info("获取下拉选项...")
        second_dropdown_options = WebDriverWait(driver, 30).until(
            EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]/li"))
        )
        
        second_options_dict = {}
        for idx, option in enumerate(second_dropdown_options):
            option_text = option.text.strip()
            if option_text and option_text != "全部":
                second_options_dict[idx] = option_text
                logger.info(f"保存第二个下拉框选项 {idx}: {option_text}")
        
        if not second_options_dict:
            logger.error("未找到有效的第二个下拉框选项")
            return None
            
        # 处理每个选项
        tab_data = {}
        for idx, second_option_text in second_options_dict.items():
            try:
                if not second_dropdown_menu.is_displayed():
                    logger.info("下拉菜单不可见，重新打开...")
                    driver.execute_script("arguments[0].scrollIntoView(true);", second_dropdown)
                    random_sleep(2, 3)  # 增加等待时间
                    
                    actions = ActionChains(driver)
                    actions.move_to_element(second_dropdown)
                    actions.click()
                    actions.perform()
                    random_sleep(3, 5)  # 增加点击后的等待时间
                    
                    second_dropdown_menu = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]"))
                    )
                    second_dropdown_options = WebDriverWait(driver, 30).until(
                        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]/li"))
                    )
                    if not second_dropdown_options:
                        logger.error("重新打开下拉菜单后未找到选项")
                        continue
                
                logger.info(f"选择第二个选项: {second_option_text}")
                
                current_option = second_dropdown_options[idx]
                driver.execute_script("arguments[0].scrollIntoView(true);", current_option)
                random_sleep(1, 2)  # 增加滚动后的等待时间
                
                actions = ActionChains(driver)
                actions.move_to_element(current_option)
                actions.click()
                actions.perform()
                
                random_sleep(4, 6)  # 增加选择选项后的等待时间
                
                # 等待页面加载完成
                logger.info("等待页面加载完成...")
                if not wait_for_page_load(driver, timeout=15):  # 增加页面加载超时时间
                    logger.warning("页面加载超时，继续处理下一个选项")
                    continue
                
                # 额外等待确保数据加载完成
                random_sleep(2, 3)
                
                # 提取数据
                logger.info("开始提取资费数据...")
                data = extract_tariff_data(driver)
                
                # 保存选择完二级分类后的页面HTML
                # 根据first_option_text区分全网资费和本省资费
                fee_type = "全网资费" if "全网" in tab_name else "本省资费"
                html_saver.save_selenium_html(
                    driver=driver,
                    province_code=province_code,
                    province_name=province_name,
                    operator="broadnet",
                    page_info=f"{fee_type}_{first_option_text}_{second_option_text}"
                )
                
                if not data:
                    logger.warning(f"未获取到资费数据: {first_option_text}_{second_option_text}，尝试切换到其他下拉再切回重试，最多3次")
                    retry_count = 0
                    max_retries = 3
                    while retry_count < max_retries and not data:
                        other_indices = [i for i in range(len(second_dropdown_options)) if i != idx]
                        if not other_indices:
                            break
                        
                        # 随机选择一个可交互的其他选项
                        random_idx = random.choice(other_indices)
                        
                        try:
                            # 先模拟点击第二个下拉框打开菜单
                            logger.info(f"第{retry_count+1}次尝试：先点击第二个下拉框打开菜单...")
                            driver.execute_script("arguments[0].scrollIntoView(true);", second_dropdown)
                            random_sleep(2, 3)
                            actions = ActionChains(driver)
                            actions.move_to_element(second_dropdown)
                            actions.click()
                            actions.perform()
                            random_sleep(3, 5)
                            
                            # 等待下拉菜单出现并重新获取选项
                            second_dropdown_menu = WebDriverWait(driver, 30).until(
                                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]"))
                            )
                            second_dropdown_options = WebDriverWait(driver, 30).until(
                                EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]/li"))
                            )
                            
                            # 获取其他选项
                            other_option = second_dropdown_options[random_idx]
                            
                            # 检查其他选项是否可交互
                            if not other_option.is_displayed() or not other_option.is_enabled():
                                logger.warning(f"其他选项不可交互，跳过")
                                other_indices.remove(random_idx)
                                if not other_indices:
                                    break
                                continue
                            
                            # 切换到其他选项
                            logger.info(f"第{retry_count+1}次尝试切换到其他选项...")
                            driver.execute_script("arguments[0].scrollIntoView(true);", other_option)
                            random_sleep(1, 2)
                            
                            # 使用JavaScript点击，避免交互问题
                            driver.execute_script("arguments[0].click();", other_option)
                            random_sleep(4, 6)
                            wait_for_page_load(driver, timeout=15)
                            random_sleep(2, 3)
                            
                            # 再次点击第二个下拉框打开菜单
                            logger.info("再次点击第二个下拉框打开菜单...")
                            driver.execute_script("arguments[0].scrollIntoView(true);", second_dropdown)
                            random_sleep(2, 3)
                            actions = ActionChains(driver)
                            actions.move_to_element(second_dropdown)
                            actions.click()
                            actions.perform()
                            random_sleep(3, 5)
                            
                            # 重新获取下拉菜单和选项
                            second_dropdown_menu = WebDriverWait(driver, 30).until(
                                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]"))
                            )
                            second_dropdown_options = WebDriverWait(driver, 30).until(
                                EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'el-select-dropdown') and contains(@class, 'label_add') and not(contains(@style, 'display: none'))]//ul[contains(@class, 'el-select-dropdown__list')]/li"))
                            )
                            current_option = second_dropdown_options[idx]
                            
                            # 切回当前选项
                            driver.execute_script("arguments[0].scrollIntoView(true);", current_option)
                            random_sleep(1, 2)
                            driver.execute_script("arguments[0].click();", current_option)
                            random_sleep(4, 6)
                            wait_for_page_load(driver, timeout=15)
                            random_sleep(2, 3)
                            
                            # 再次尝试抓取
                            logger.info(f"第{retry_count+1}次切回当前下拉选项后再次尝试提取资费数据...")
                            data = extract_tariff_data(driver)
                            retry_count += 1
                            
                        except Exception as e:
                            logger.error(f"第{retry_count+1}次切换下拉选项时出错: {str(e)}")
                            retry_count += 1
                            # 尝试重新加载页面
                            try:
                                logger.info("尝试重新加载页面...")
                                driver.refresh()
                                random_sleep(3, 5)
                                if wait_for_page_load(driver, timeout=15):
                                    logger.info("页面重新加载成功")
                                else:
                                    logger.warning("页面重新加载失败")
                            except Exception as refresh_error:
                                logger.error(f"重新加载页面时出错: {str(refresh_error)}")
                            continue
                if data:
                    key = f"{first_option_text}_{second_option_text}"
                    tab_data[key] = data
                    logger.info(f"成功获取资费数据: {key}")
                else:
                    logger.warning(f"未获取到资费数据: {first_option_text}_{second_option_text}")
                    
            except Exception as e:
                logger.error(f"处理选项 {second_option_text} 时出错: {str(e)}")
                # 尝试重新加载页面
                try:
                    logger.info("尝试重新加载页面...")
                    driver.refresh()
                    random_sleep(3, 5)  # 增加页面刷新后的等待时间
                    if wait_for_page_load(driver, timeout=15):
                        logger.info("页面重新加载成功")
                    else:
                        logger.warning("页面重新加载失败")
                except Exception as refresh_error:
                    logger.error(f"重新加载页面时出错: {str(refresh_error)}")
                continue
        
        if not tab_data:
            logger.warning(f"未获取到任何数据: {first_option_text}")
            return None
            
        # 获取省份名称
        province_name = None
        province_selectors = [
            (By.XPATH, "//div[contains(@class, 'el-select')]//input[@type='text']"),
            (By.XPATH, "//div[contains(@class, 'el-select')]//span[contains(@class, 'el-input__inner')]"),
            (By.XPATH, "//div[contains(@class, 'el-select')]//div[contains(@class, 'el-input__inner')]"),
            (By.XPATH, "//div[contains(@class, 'el-select')]//div[contains(@class, 'el-input')]")
        ]
        
        for by, selector in province_selectors:
            try:
                element = driver.find_element(by, selector)
                province_name = element.get_attribute("value")
                if not province_name:
                    province_name = element.text
                if province_name:
                    logger.info(f"成功获取省份名称: {province_name}")
                    break
            except:
                continue
            
        return tab_data
        
    except Exception as e:
        logger.error(f"处理第二个下拉框时出错: {str(e)}")
        return None

async def handle_province_selection(driver, callback_url, target_provinces=None, zip_file_path=None, province_code="", province_name=""):
    """
    处理省份选择的逻辑，获取指定省份的数据
    
    Args:
        driver: WebDriver实例
        callback_url: 回调URL
        target_provinces: 要爬取的省份列表，如果为None则爬取所有省份
        
    Returns:
        bool: 处理是否成功
    """
    logger.info("开始处理省份选择...")
    try:
        # 确保目录结构存在
        ensure_directories()
        # 等待页面加载完成
        logger.info("等待页面加载完成...")
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "el-select"))
        )
        logger.info("页面加载完成")
        
        # 滚动到页面顶部
        logger.info("滚动到页面顶部...")
        driver.execute_script("window.scrollTo(0, 0);")
        
        # 尝试多种方式定位下拉箭头图标
        logger.info("开始定位下拉箭头图标...")
        dropdown_icon = None
        icon_selectors = [
            (By.CSS_SELECTOR, "i.el-icon-caret-bottom"),
            (By.CSS_SELECTOR, ".el-select i.el-icon-caret-bottom"),
            (By.XPATH, "//i[contains(@class, 'el-icon-caret-bottom')]"),
            (By.XPATH, "//div[contains(@class, 'el-select')]//i[contains(@class, 'el-icon-caret-bottom')]")
        ]
        
        for by, selector in icon_selectors:
            try:
                logger.info(f"尝试使用选择器: {selector}")
                dropdown_icon = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((by, selector))
                )
                if dropdown_icon:
                    logger.info(f"成功找到下拉箭头图标: {selector}")
                    break
            except Exception as e:
                logger.debug(f"使用选择器 {selector} 未找到元素: {str(e)}")
                continue
        
        if not dropdown_icon:
            logger.error("未找到下拉箭头图标")
            return False
            
        # 确保元素可见和可点击
        logger.info("确保下拉箭头图标可见和可点击...")
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", dropdown_icon)
        time.sleep(1)
        
        # 尝试多种方式点击
        click_methods = [
            lambda: driver.execute_script("arguments[0].click();", dropdown_icon),
            lambda: ActionChains(driver).move_to_element(dropdown_icon).click().perform()
        ]
        
        for i, click_method in enumerate(click_methods):
            try:
                logger.info(f"尝试第 {i+1} 种点击方式...")
                click_method()
                time.sleep(1)
                
                # 验证点击是否成功
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "van-cascader"))
                    )
                    logger.info(f"第 {i+1} 种点击方式成功")
                    break
                except:
                    logger.debug(f"第 {i+1} 种点击方式未触发下拉菜单")
                    continue
                    
            except Exception as e:
                logger.debug(f"第 {i+1} 种点击方式失败: {str(e)}")
                continue
        
        # 等待级联选择器出现
        logger.info("等待级联选择器出现...")
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "van-cascader"))
            )
            logger.info("级联选择器已出现")
        except Exception as e:
            logger.error(f"等待级联选择器超时: {str(e)}")
            return False
        
        # 获取所有省份选项
        logger.info("开始获取省份选项...")
        province_options = WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul.van-cascader__options li.van-cascader__option span"))
        )
        
        if not province_options:
            logger.error("未找到任何省份选项")
            return False
            
        logger.info(f"找到 {len(province_options)} 个省份选项")
        
        # 存储所有省份数据
        all_provinces_data = {}
        
        # 依次处理每个省份
        for province_option in province_options:
            try:
                province_name = province_option.text.strip()
                if not province_name:
                    continue
                
                # 如果指定了目标省份，则只处理指定的省份
                if target_provinces and province_name not in target_provinces:
                    logger.info(f"跳过非目标省份: {province_name}")
                    continue
                    
                logger.info(f"开始处理省份: {province_name}")
                
                # 点击省份选项
                logger.info(f"点击省份选项: {province_name}")
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", province_option)
                time.sleep(0.5)
                driver.execute_script("arguments[0].click();", province_option)
                time.sleep(2)  # 等待选择生效
                
                # 处理当前省份的选项卡和下拉框
                logger.info(f"开始处理 {province_name} 的选项卡和下拉框...")
                province_data = await handle_tabs_and_dropdowns_for_province(driver, province_name, province_code)
                if province_data:
                    all_provinces_data[province_name] = province_data
                    logger.info(f"成功获取 {province_name} 的数据")
                    
                    # 保存当前省份的数据
                    saved_files = []
                    for tab_name, tab_data in province_data.items():
                        logger.info(f"保存 {province_name} 的 {tab_name} 数据...")
                        file_path = save_data_to_files(province_name, tab_name, tab_data)
                        if file_path:
                            saved_files.append(file_path)
                    
                    # 如果成功保存了两个文件，创建zip文件
                    if len(saved_files) == 2:
                        logger.info(f"为 {province_name} 创建ZIP文件...")
                        # 调用create_province_zip时直接用zip_file_path
                        zip_file = create_province_zip(province_name, saved_files, zip_file_path)
                        if zip_file and callback_url:
                            logger.info(f"发送 {province_name} 的ZIP文件到回调URL...")
                            callback_result=callback(
                                callback_url,
                                zip_file,
                                "https://m.10099.com.cn/expensesNotice/#/home",
                                "200"
                            )
                            logger.info(f"回调结果: {callback_result}")
                    
            except Exception as e:
                logger.error(f"处理省份 {province_name} 时出错: {str(e)}")
                continue
        
        return True
        
    except Exception as e:
        logger.error(f"处理省份选择时出错: {str(e)}")
        return False

async def handle_tabs_and_dropdowns_for_province(driver, province_name, province_code=""):
    """
    处理选项卡和下拉框的选择，针对特定省份
    
    Args:
        driver: WebDriver实例
        province_name: 当前处理的省份名称
        
    Returns:
        dict: 处理后的数据，如果处理失败则返回None
    """
    logger.info(f"开始处理 {province_name} 的选项卡和下拉框...")
    all_province_data = {}
    try:
        top_element = wait_for_element(driver, By.CLASS_NAME, "top")
        if not top_element:
            logger.error("未找到top元素")
            return None
            
        logger.info("已找到top元素")
        
        tabs = top_element.find_elements(By.XPATH, ".//div[not(contains(@class, 'bor'))]")
        if not tabs:
            logger.error("未找到选项卡")
            return None
            
        logger.info(f"找到 {len(tabs)} 个选项卡")
        
        for tab_index, tab in enumerate(tabs):
            try:
                tab_name = tab.text.strip()
                if not tab_name:
                    continue
                    
                logger.info(f"处理选项卡 {tab_index + 1}: {tab_name}")
                
                tab_data = await process_tab_data(driver, tab, tab_name, province_code=province_code, province_name=province_name)
                if tab_data:
                    all_province_data[tab_name] = tab_data
                        
            except Exception as e:
                logger.error(f"处理选项卡时出错: {str(e)}")
                continue
        return all_province_data
        
    except Exception as e:
        logger.error(f"处理选项卡和下拉框时出错: {str(e)}")
        return None

def find_fee_link(driver):
    """
    查找资费公示链接
    
    Args:
        driver: WebDriver实例
        
    Returns:
        WebElement: 资费公示链接元素，如果未找到则返回None
    """
    logger.info("正在查找底部导航栏中的资费公示链接...")
    bottom_nav = wait_for_element(driver, By.ID, "bottom-navigation-bar")
    if not bottom_nav:
        logger.error("未找到底部导航栏")
        return None
        
    logger.info("已找到底部导航栏，开始查找资费公示链接")
    
    link_selectors = [
        (By.XPATH, "//div[@id='bottom-navigation-bar']//span[contains(@class, 'spanlink') and contains(text(), '资费公示')]"),
        (By.XPATH, "//div[@id='bottom-navigation-bar']//div[contains(@class, 'navigation-title') and text()='产品资费']/following-sibling::div//span[contains(@class, 'spanlink')]"),
        (By.XPATH, "//div[@id='bottom-navigation-bar']//span[contains(text(), '资费公示')]"),
        (By.CLASS_NAME, "spanlink")
    ]
    
    for by, selector in link_selectors:
        try:
            logger.info(f"尝试定位资费公示链接: {selector}")
            elements = driver.find_elements(by, selector)
            for element in elements:
                if element.text.strip() == "资费公示":
                    logger.info(f"成功找到资费公示链接: {selector}")
                    return element
        except NoSuchElementException:
            logger.debug(f"使用选择器 {selector} 未找到链接")
            continue
    
    logger.error("所有选择器都未能找到资费公示链接")
    return None

def click_fee_link(driver, fee_link):
    """
    点击资费公示链接
    
    Args:
        driver: WebDriver实例
        fee_link: 资费公示链接元素
        
    Returns:
        bool: 是否成功点击链接
    """
    try:
        logger.info("正在点击资费公示链接...")
        driver.execute_script("arguments[0].scrollIntoView(true);", fee_link)
        random_sleep(1, 2)
        
        parent_div = fee_link.find_element(By.XPATH, "./ancestor::div[contains(@class, 'navigation-link')]")
        logger.info("找到父元素")
        
        actions = ActionChains(driver)
        actions.move_to_element(parent_div)
        random_sleep(0.5, 1)
        
        actions.move_to_element(fee_link)
        random_sleep(0.5, 1)
        
        actions.click()
        actions.perform()
        
        random_sleep(3, 5)
        
        current_url = driver.current_url
        logger.info(f"当前页面URL: {current_url}")
        
        random_sleep(3, 5)
        return True
        
    except Exception as e:
        logger.error(f"点击链接失败: {str(e)}")
        return False

def callback(callback_url: str, file: str, crawler_url: str, status: str = "200") -> dict:
    """回调函数，将zip文件通过FormData发送到指定的URL
    
    Args:
        callback_url: 回调URL地址
        file: zip文件路径
        crawler_url: 爬虫URL地址
        status: 爬虫状态，默认为"200"，失败时为"500"
        
    Returns:
        dict: 回调响应的JSON数据
    """
    return async_callback(callback_url, file, crawler_url, status)

async def main(provinces: str = None, callback_url: str = None):
    """
    主函数：执行自动化任务
    """
    logger.info("开始执行自动化任务")
    max_retries = 10
    retry_count = 0
    last_error = None
    task_success = False
    zip_file_path = ""
    # 处理省份参数
    target_provinces = None
    if provinces:
        target_provinces = [p.strip() for p in provinces.split(",")]
        logger.info(f"指定爬取以下省份: {target_provinces}")
    # ========== 路径生成开始 ==========
    import random
    from datetime import datetime
    province_name = target_provinces[0] if target_provinces else 'unknown'
    rand_str = str(random.randint(1000, 9999))
    base_dir = ensure_directories()
    zip_dir = os.path.join(base_dir, "zip")
    zip_filename = f"{province_name}_broadnet_{rand_str}_资费数据.zip"
    # main方法生成zip_file_path和mp4_path
    zip_file_path = os.path.join(zip_dir, zip_filename)
    base_mp4_path = os.path.splitext(zip_file_path)[0] + ".mp4"
    while retry_count < max_retries:
        driver = None
        screen_recorder = None
        mp4_path = base_mp4_path.replace('.mp4', f'_retry{retry_count+1}.mp4')
        try:
            # 确保目录结构存在
            ensure_directories()
            # 检查是否启用录屏
            if is_screen_recording_enabled():
                # 每次重试都新建录屏
                screen_recorder = ScreenRecorder(output_file=mp4_path, capture_method='dshow')
                screen_recorder.start_recording()
                logger.info(f"录屏已开始，文件: {mp4_path}")
            else:
                logger.info("录屏功能已禁用")
            driver = setup_driver()
            
            # 调整浏览器窗口位置和大小
            window_width, window_height = get_adaptive_window_size()
            driver.set_window_size(window_width, window_height)
            
            # 等待浏览器完全打开并加载页面
            time.sleep(3)  # 等待浏览器稳定
            logger.info("浏览器页面加载完成")
            
            # 调整Anaconda Prompt窗口到右半屏
            anaconda_window_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
            split_window_to_right_half(anaconda_window_titles, show_debug=True)
            
            # 激活并置顶Anaconda Prompt窗口
            activate_window(anaconda_window_titles, show_debug=True)
            
            logger.info("Anaconda Prompt窗口已调整到右半屏并置顶")
            
            logger.info("正在打开中国广电资费公示页面...")
            try:
                driver.get('https://m.10099.com.cn/expensesNotice/#/home')
                logger.info("成功打开中国广电资费公示页面")
            except Exception as e:
                logger.error(f"打开网站失败: {str(e)}")
                raise
            random_sleep(2, 4)
            # 处理指定省份的数据
            if not await handle_province_selection(driver, callback_url, target_provinces, zip_file_path, province_code=provinces, province_name=target_provinces[0] if target_provinces else "unknown"):
                raise Exception("处理省份选择和数据抓取失败")
            task_success = True
            return True
        except Exception as e:
            last_error = e
            retry_count += 1
            logger.error(f"第 {retry_count} 次尝试失败: {str(e)}")
            if retry_count < max_retries:
                logger.info(f"等待 5 秒后开始第 {retry_count + 1} 次尝试...")
                await asyncio.sleep(5)
            else:
                logger.error(f"已达到最大重试次数 ({max_retries})，任务失败")
                return None
        finally:
            if driver:
                try:
                    logger.info("正在关闭浏览器...")
                    driver.quit()
                    logger.info("浏览器已关闭")
                except Exception as e:
                    logger.error(f"关闭浏览器时出现错误: {str(e)}")
            if screen_recorder:
                try:
                    screen_recorder.stop_recording()
                    logger.info(f"录屏已结束，文件: {mp4_path}")
                except Exception as e:
                    logger.error(f"录屏结束失败: {str(e)}")
            # 统一回调
            if callback_url:
                status = "200" if task_success else "500"
                file_path = zip_file_path if task_success else ""
                try:
                    callback(callback_url, file_path, "https://m.10099.com.cn/expensesNotice/#/home", status)
                    logger.info(f"已在finally中回调，状态: {status}")
                except Exception as cb_e:
                    logger.error(f"回调时出现异常: {str(cb_e)}")
    logger.info("自动化任务完成")
    return None

if __name__ == "__main__":
    import asyncio
    with open('provinces.txt', 'r', encoding='utf-8') as f:
        provinces = f.readlines()
        for province in provinces:
            asyncio.run(main(provinces=province.strip()))


