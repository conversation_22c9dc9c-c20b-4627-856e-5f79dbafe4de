#!/usr/bin/env python3
"""
窗口操作工具模块
提供各种窗口操作的便捷方法
"""

import pygetwindow as gw
import pyautogui
from common.logger_config import logger


def get_screen_size():
    """
    获取屏幕尺寸
    
    Returns:
        tuple: (width, height) 屏幕宽度和高度
    """
    try:
        width, height = pyautogui.size()
        logger.debug(f"屏幕尺寸: {width} x {height}")
        return width, height
    except Exception as e:
        logger.error(f"获取屏幕尺寸失败: {e}")
        return 1920, 1080  # 默认值


def get_adaptive_window_size():
    """
    获取自适应的窗口尺寸
    
    Returns:
        tuple: (width, height) 自适应窗口宽度和高度
    """
    screen_width, screen_height = get_screen_size()
    
    # 窗口宽度为屏幕宽度的80%，高度为屏幕高度的80%
    window_width = int(screen_width * 0.8)
    window_height = int(screen_height * 0.8)
    
    logger.debug(f"自适应窗口尺寸: {window_width} x {window_height}")
    return window_width, window_height


def split_window_to_right_half(window_titles, show_debug=False):
    """
    将指定窗口调整为右半屏分屏模式
    
    Args:
        window_titles (list): 要查找的窗口标题列表
        show_debug (bool): 是否显示调试信息，默认为 False
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        if show_debug:
            logger.debug(f"屏幕尺寸: {screen_width} x {screen_height}")
        
        # 查找目标窗口
        target_window = None
        
        for title in window_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                target_window = windows[0]
                if show_debug:
                    logger.debug(f"找到目标窗口: '{target_window.title}'")
                break
        
        if target_window:
            if show_debug:
                logger.debug(f"原始窗口尺寸: {target_window.width} x {target_window.height}")
                logger.debug(f"原始窗口位置: ({target_window.left}, {target_window.top})")
            
            # 计算右半屏的尺寸和位置
            new_width = int(screen_width * 0.3)  # 屏幕宽度的30%
            new_height = int(screen_height - 40)  # 屏幕高度减去任务栏
            x = screen_width - new_width  # 右半屏位置
            y = 0  # 从顶部开始
            
            if show_debug:
                logger.debug(f"目标尺寸: {new_width} x {new_height}")
                logger.debug(f"目标位置: ({x}, {y})")
            
            # 移动和调整窗口
            target_window.moveTo(x, y)
            target_window.resizeTo(new_width, new_height)
            target_window.activate()
            
            if show_debug:
                logger.info(f"窗口 '{target_window.title}' 已成功调整为右半屏分屏模式")
            
            return True
            
        else:
            if show_debug:
                logger.warning(f"未找到指定窗口: {window_titles}")
            return False

    except Exception as e:
        if show_debug:
            logger.error(f"执行出错: {e}")
        return False


def split_anaconda_prompt_to_right_half(show_debug=False):
    """
    将 Anaconda Prompt 窗口调整为右半屏分屏模式
    
    Args:
        show_debug (bool): 是否显示调试信息，默认为 False
    
    Returns:
        bool: 操作是否成功
    """
    anaconda_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
    return split_window_to_right_half(anaconda_titles, show_debug)


def move_window_to_bottom_right(window_titles, show_debug=False):
    """
    将指定窗口移动到右下角
    
    Args:
        window_titles (list): 要查找的窗口标题列表
        show_debug (bool): 是否显示调试信息，默认为 False
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        
        # 查找目标窗口
        target_window = None
        
        for title in window_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                target_window = windows[0]
                if show_debug:
                    logger.debug(f"找到目标窗口: '{target_window.title}'")
                break
        
        if target_window:
            # 计算右下角位置
            x = screen_width - target_window.width - 10  # 右边留10像素边距
            y = screen_height - target_window.height - 50  # 底部留50像素边距
            
            # 移动窗口
            target_window.moveTo(x, y)
            target_window.activate()
            
            if show_debug:
                logger.info(f"窗口 '{target_window.title}' 已移动到右下角位置 ({x}, {y})")
            
            return True
            
        else:
            if show_debug:
                logger.warning(f"未找到指定窗口: {window_titles}")
            return False

    except Exception as e:
        if show_debug:
            logger.error(f"执行出错: {e}")
        return False


def list_all_windows():
    """
    列出所有当前打开的窗口
    
    Returns:
        list: 窗口信息列表
    """
    try:
        all_windows = gw.getAllWindows()
        window_info = []
        
        for i, win in enumerate(all_windows):
            info = {
                'index': i + 1,
                'title': win.title,
                'width': win.width,
                'height': win.height,
                'left': win.left,
                'top': win.top
            }
            window_info.append(info)
        
        return window_info
    
    except Exception as e:
        logger.error(f"获取窗口列表失败: {e}")
        return []


def activate_window(window_titles, show_debug=False):
    """
    激活指定窗口并置顶
    
    Args:
        window_titles (list): 要查找的窗口标题列表
        show_debug (bool): 是否显示调试信息，默认为 False
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 查找目标窗口
        target_window = None
        
        for title in window_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                target_window = windows[0]
                if show_debug:
                    logger.debug(f"找到目标窗口: '{target_window.title}'")
                break
        
        if target_window:
            # 激活窗口并置顶
            target_window.activate()
            target_window.raise_()
            
            if show_debug:
                logger.info(f"窗口 '{target_window.title}' 已激活并置顶")
            
            return True
            
        else:
            if show_debug:
                logger.warning(f"未找到指定窗口: {window_titles}")
            return False

    except Exception as e:
        if show_debug:
            logger.error(f"激活窗口时出错: {e}")
        return False


# 使用示例
if __name__ == "__main__":
    logger.info("=== 窗口操作工具演示 ===")
    
    # 示例1: 将 Anaconda Prompt 调整为右半屏
    logger.info("\n1. 将 Anaconda Prompt 调整为右半屏:")
    success = split_anaconda_prompt_to_right_half(show_debug=True)
    logger.info(f"操作结果: {'成功' if success else '失败'}")
    
    # 示例2: 列出所有窗口
    logger.info("\n2. 当前所有窗口:")
    windows = list_all_windows()
    for win in windows[:5]:  # 只显示前5个
        logger.info(f"  {win['index']}. '{win['title']}' (大小: {win['width']}x{win['height']})")
    
    logger.info("\n=== 演示完毕 ===")
