2025-08-04 14:37:22 - MainProcess - 3400 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:37:23 - SpawnProcess-1 - 2136 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:39:33 - MainProcess - 46572 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (96, 96)
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 14:39:47 - MainProcess - 46572 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:39:47 - MainProcess - 46572 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:39:47 - MainProcess - 46572 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:39:56 - MainProcess - 46572 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:39:56 - MainProcess - 46572 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:40:26 - MainProcess - 25928 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:40:28 - SpawnProcess-1 - 19960 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:40:30 - MainProcess - 37280 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 14:40:37 - MainProcess - 37280 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:40:37 - MainProcess - 37280 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:40:37 - MainProcess - 37280 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:40:46 - MainProcess - 37280 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:40:46 - MainProcess - 37280 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 14:42:07 - MainProcess - 48860 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:42:08 - SpawnProcess-1 - 24712 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:42:10 - MainProcess - 21068 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 14:42:17 - MainProcess - 21068 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:42:17 - MainProcess - 21068 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:42:17 - MainProcess - 21068 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 14:43:02 - MainProcess - 51432 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:43:03 - SpawnProcess-1 - 52212 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:43:21 - MainProcess - 28068 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 14:43:29 - MainProcess - 28068 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:43:29 - MainProcess - 28068 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:43:29 - MainProcess - 28068 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:43:38 - MainProcess - 28068 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:43:38 - MainProcess - 28068 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 14:44:09 - MainProcess - 28068 - common.callback_utils - WARNING - [callback_utils.py:160] - 未提供文件路径
2025-08-04 14:44:09 - MainProcess - 28068 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-04 14:44:19 - MainProcess - 17044 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:44:20 - SpawnProcess-1 - 23972 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:44:45 - MainProcess - 54068 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 14:44:52 - MainProcess - 54068 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 14:44:52 - MainProcess - 54068 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:44:52 - MainProcess - 54068 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 14:45:00 - MainProcess - 54068 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 14:45:00 - MainProcess - 54068 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 16:18:05 - MainProcess - 25900 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:18:07 - SpawnProcess-1 - 57916 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:18:09 - MainProcess - 29384 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (96, 96)
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 16:18:24 - MainProcess - 29384 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 16:18:24 - MainProcess - 29384 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:18:24 - MainProcess - 29384 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 16:18:33 - MainProcess - 29384 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:18:33 - MainProcess - 29384 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 16:18:57 - MainProcess - 29384 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 17733 字节
2025-08-04 16:18:57 - MainProcess - 29384 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-04 16:22:21 - MainProcess - 35324 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:22:22 - SpawnProcess-1 - 21332 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:22:24 - MainProcess - 16464 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1038
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 16:22:31 - MainProcess - 16464 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 16:22:31 - MainProcess - 16464 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:22:31 - MainProcess - 16464 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 16:22:39 - MainProcess - 16464 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 16:22:39 - MainProcess - 16464 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 16:29:45 - MainProcess - 16464 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 221425 字节
2025-08-04 16:29:45 - MainProcess - 16464 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-04 19:07:48 - MainProcess - 9284 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:07:50 - SpawnProcess-1 - 32572 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:07:53 - MainProcess - 56356 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:08:44 - MainProcess - 56356 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:08:44 - MainProcess - 56356 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 19:09:06 - MainProcess - 43964 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:09:07 - SpawnProcess-1 - 53680 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:09:15 - MainProcess - 17968 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 860
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 178)
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 19:09:22 - MainProcess - 17968 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 19:09:22 - MainProcess - 17968 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:09:22 - MainProcess - 17968 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 19:09:32 - MainProcess - 17968 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:09:32 - MainProcess - 17968 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:09:58 - MainProcess - 24088 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:09:59 - SpawnProcess-1 - 16860 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:10:01 - MainProcess - 16900 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:10:02 - MainProcess - 32512 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 19:10:08 - MainProcess - 16900 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 19:10:08 - MainProcess - 16900 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:08 - MainProcess - 16900 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:10:09 - MainProcess - 32512 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:10:09 - MainProcess - 32512 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 19:10:09 - MainProcess - 32512 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 19:10:10 - MainProcess - 32512 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-08-04 19:10:10 - MainProcess - 32512 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:10 - MainProcess - 32512 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:10:18 - MainProcess - 32512 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:18 - MainProcess - 32512 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:10:26 - MainProcess - 16900 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:10:26 - MainProcess - 16900 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:10:32 - MainProcess - 16900 - common.callback_utils - WARNING - [callback_utils.py:160] - 未提供文件路径
2025-08-04 19:10:32 - MainProcess - 16900 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-04 19:10:36 - MainProcess - 32512 - common.callback_utils - WARNING - [callback_utils.py:160] - 未提供文件路径
2025-08-04 19:10:36 - MainProcess - 32512 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
2025-08-04 19:10:40 - MainProcess - 2012 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:10:41 - SpawnProcess-1 - 35176 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:10:44 - MainProcess - 14724 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:11:14 - MainProcess - 25180 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:11:15 - SpawnProcess-1 - 46880 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:11:18 - MainProcess - 3708 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250804
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 576 x 1040
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (1344, 0)
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-04 19:11:35 - MainProcess - 3708 - common - ERROR - [window_utils.py:105] - 执行出错: Error code from Windows: 0 - 操作成功完成。
2025-08-04 19:11:35 - MainProcess - 3708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:11:35 - MainProcess - 3708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:11:44 - MainProcess - 3708 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-04 19:11:44 - MainProcess - 3708 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-04 19:12:15 - MainProcess - 3708 - common.callback_utils - INFO - [callback_utils.py:38] - 文件大小: 32222 字节
2025-08-04 19:12:15 - MainProcess - 3708 - common.callback_utils - ERROR - [callback_utils.py:182] - 发送回调时发生错误: 123
