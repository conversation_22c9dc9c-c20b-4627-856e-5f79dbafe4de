import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional

def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """加载配置文件
    
    Args:
        config_path: 配置文件路径，如果为None则使用默认路径
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if config_path is None:
        # 使用项目根目录下的config.yml
        config_path = Path(__file__).parent.parent / "config.yml"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"配置文件不存在: {config_path}")
        return {}
    except Exception as e:
        print(f"加载配置文件时发生错误: {str(e)}")
        return {}

def is_screen_recording_enabled() -> bool:
    """检查是否启用录屏功能
    
    Returns:
        bool: 是否启用录屏
    """
    config = load_config()
    return config.get('screen_recording', {}).get('enabled', True)

# 创建全局配置实例
config = load_config() 