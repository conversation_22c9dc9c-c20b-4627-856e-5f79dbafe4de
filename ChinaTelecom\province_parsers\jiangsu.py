from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_jiangsu_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析江苏资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析江苏{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.pro_list.showList", timeout=10000)
        
        # 存储所有产品数据
        all_products = []
        
        # 获取所有产品项
        products = await page.query_selector_all("div.pro_list.showList > div.lay")
        
        if not products:
            logger.warning("未找到产品项")
            return
            
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.title > p")
                code_elem = await product.query_selector("div.title > span.serial")
                
                if not title_elem or not code_elem:
                    logger.error(f"第 {index} 个产品未找到标题或编号元素")
                    continue
                    
                title = await title_elem.text_content()
                code = await code_elem.text_content()
                
                # 提取方案编号
                if "方案编号：" in code:
                    code = code.split("方案编号：")[1].strip()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"产品编号: {code}")
                
                # 获取基本信息
                basic_info = {}
                info_box = await product.query_selector("div.floor:first-child > div.box")
                if info_box:
                    info_items = await info_box.query_selector_all("div.lay_s")
                    for item in info_items:
                        try:
                            label = await item.query_selector("span")
                            value = await item.query_selector("div.right")
                            if label and value:
                                key = await label.text_content()
                                val = await value.text_content()
                                # 移除冒号
                                key = key.replace("：", "").strip()
                                basic_info[key] = val.strip()
                        except Exception as e:
                            logger.error(f"解析基本信息项时发生错误: {str(e)}")
                            continue
                
                logger.info(f"基本信息: {basic_info}")
                
                # 获取服务内容
                service_content = {}
                service_table = await product.query_selector("div.floor:nth-child(2) > table.table")
                if service_table:
                    headers = await service_table.query_selector_all("thead th")
                    values = await service_table.query_selector_all("tbody td")
                    
                    for i in range(min(len(headers), len(values))):
                        header = await headers[i].text_content()
                        value = await values[i].text_content()
                        if header and value and header != "-" and value != "-":
                            service_content[header.strip()] = value.strip()
                
                logger.info(f"服务内容: {service_content}")
                
                # 获取资费说明
                fee_rules = []
                rule_divs = await product.query_selector_all("div.floor:nth-child(2) > div.rule")
                for rule_div in rule_divs:
                    rule_text = await rule_div.text_content()
                    if rule_text and rule_text.strip():
                        fee_rules.append(rule_text.strip())
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "fee_rules": fee_rules
                }
                
                all_products.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(all_products)}个")
        
        if all_products:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = all_products
            logger.info(f"成功保存{menu_type}的{len(all_products)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_jiangsu_menu_config() -> dict:
    """获取江苏的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.nav_main",
        'menu_selector': "div.tab > span.lay",  # 主菜单选择器
        'list_selector': "div.pro_list.showList"
    } 