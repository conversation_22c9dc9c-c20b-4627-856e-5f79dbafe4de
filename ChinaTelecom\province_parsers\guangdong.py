from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_guangdong_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析广东资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析广东{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.w-full.flex.flex-col.items-center.justify-center", timeout=10000)
        
        # 先最大化浏览器窗口
        logger.info("最大化浏览器窗口...")
        await page.evaluate("""() => {
            window.moveTo(0, 0);
            window.resizeTo(screen.availWidth, screen.availHeight);
        }""")
        await page.wait_for_timeout(2000)  # 等待窗口调整完成
        
        # 处理"查看更多"按钮
        logger.info("开始处理'查看更多'按钮...")
        max_retries = 200  # 增加重试次数，因为可能需要多次点击
        retry_count = 0
        last_product_count = 0
        same_count_times = 0
        
        while retry_count < max_retries:
            try:
                # 获取当前产品数量
                current_products = await page.query_selector_all("div.w-full.header-content > div")
                current_count = len(current_products)
                logger.info(f"当前加载产品数量: {current_count}")
                
                # 检查产品数量是否变化
                if current_count == last_product_count:
                    same_count_times += 1
                    logger.info(f"产品数量未变化，连续 {same_count_times} 次")
                    if same_count_times >= 3:  # 连续3次数量不变，认为加载完成
                        logger.info("产品数量连续3次未变化，认为加载完成")
                        break
                else:
                    same_count_times = 0
                    last_product_count = current_count
                    logger.info(f"发现新数据，当前总数: {current_count}")
                
                # 查找并点击"查看更多"按钮
                load_more_button = await page.query_selector("div.w-24.text-lg.cursor-pointer.flex.items-center.justify-center.more-btn")
                if not load_more_button:
                    logger.info("未找到'查看更多'按钮，可能已加载全部内容")
                    break
                
                # 确保按钮可见
                await load_more_button.wait_for_element_state('visible')
                
                # 滚动到底部
                logger.info("滚动到底部...")
                await page.evaluate("""() => {
                    window.scrollTo(0, document.body.scrollHeight);
                }""")
                await page.wait_for_timeout(2000)  # 等待滚动完成
                
                # 使用JavaScript点击按钮
                await page.evaluate("""(button) => {
                    const event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    button.dispatchEvent(event);
                }""", load_more_button)
                logger.info("点击'查看更多'按钮")
                
                # 等待新内容加载
                await page.wait_for_timeout(3000)  # 增加等待时间
                
                retry_count += 1
                logger.info(f"第 {retry_count} 次点击'查看更多'按钮")
                
            except Exception as e:
                logger.error(f"点击'查看更多'按钮时发生错误: {str(e)}")
                retry_count += 1
                continue
        
        logger.info("内容加载完成")
        
        # 获取所有产品项
        products = await page.query_selector_all("div.w-full.header-content > div")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        logger.info(f"开始解析{menu_type}产品，共{total_products}个")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.detail-header span.text-xl.font-bold")
                code_elem = await product.query_selector("div.detail-header span:last-child")
                
                if not title_elem or not code_elem:
                    logger.error(f"第 {index} 个产品未找到标题或编号元素")
                    continue
                
                title = await title_elem.text_content()
                code = await code_elem.text_content()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"产品编号: {code}")
                
                # 提取方案编号，删除空格
                if "方案编号：" in code:
                    code = code.split("方案编号：")[1].strip()
                code = code.replace(" ", "")
                
                # 获取基本信息
                basic_info = {}
                info_rows = await product.query_selector_all("div.w-full.flex.items-center")
                for row in info_rows:
                    try:
                        # 使用JavaScript获取标签和值
                        row_data = await page.evaluate("""(row) => {
                            const labels = Array.from(row.querySelectorAll('div[class*="w-1/5"]'));
                            const values = Array.from(row.querySelectorAll('div[class*="w-4/5"], div[class*="w-3/4"]'));
                            return labels.map((label, i) => ({
                                label: label.textContent.trim(),
                                value: values[i] ? values[i].textContent.trim() : ''
                            }));
                        }""", row)
                        
                        for item in row_data:
                            if item['label'] and item['value']:
                                label = item['label'].replace("：", "").strip()
                                basic_info[label] = item['value']
                                logger.info(f"基本信息 - {label}: {item['value']}")
                    except Exception as e:
                        logger.error(f"解析基本信息行时发生错误: {str(e)}")
                
                # 获取服务内容
                service_content = {}
                # 使用JavaScript获取服务内容项
                service_items_data = await page.evaluate("""(product) => {
                    const items = Array.from(product.querySelectorAll('div[class*="w-32"], div[class*="min-w-32"]'));
                    return items.map(item => {
                        const label = item.querySelector('div[class*="text-blue"]');
                        const value = item.querySelector('div:not([class*="text-blue"])');
                        return {
                            label: label ? label.textContent.trim() : '',
                            value: value ? value.textContent.trim() : ''
                        };
                    });
                }""", product)
                
                for item_data in service_items_data:
                    if item_data['label'] and item_data['value']:
                        service_content[item_data['label']] = item_data['value']
                        logger.info(f"服务内容 - {item_data['label']}: {item_data['value']}")
                
                # 获取备注信息
                remark = ""
                remark_elem = await product.query_selector("div.w-full.p-4.text-gray-700.flex.flex-col.items-center div.w-full.text-left.truncate")
                if remark_elem:
                    remark = await remark_elem.text_content()
                    logger.info(f"备注信息: {remark[:100]}...")  # 只记录前100个字符
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_guangdong_menu_config() -> dict:
    """获取广东的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.w-full.p-4.header-content",
        'menu_selector': "div.w-full.flex.items-center > div.px-2.mt-1.mb-4.text-base.text-gray-400.cursor-pointer.relative",
        'list_selector': "div.w-full.flex.flex-col.items-center.justify-center"
    } 