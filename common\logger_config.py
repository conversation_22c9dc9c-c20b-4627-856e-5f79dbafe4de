import logging
from logging.handlers import <PERSON><PERSON>ting<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ue<PERSON><PERSON><PERSON>, QueueListener
import os
from datetime import datetime
import threading
import sys

class Logger:
    _queue_listener = None  # 类变量，确保全局只启动一次监听器
    _log_queue = None
    _lock = threading.Lock()  # 添加线程锁

    def __init__(self, name='spider', operator='common', log_dir='logs'):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            operator: 运营商名称 (mobile/telecom/unicom/broadnet)
            log_dir: 日志目录名称
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 获取当前文件所在目录的绝对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        
        # 创建运营商特定的日志目录
        operator_log_dir = os.path.join(project_root, operator, log_dir)
        if not os.path.exists(operator_log_dir):
            os.makedirs(operator_log_dir)
            
        # 创建日期子文件夹
        current_date = datetime.now().strftime('%Y%m%d')
        date_log_dir = os.path.join(operator_log_dir, current_date)
        if not os.path.exists(date_log_dir):
            os.makedirs(date_log_dir)
            
        # 生成日志文件名
        log_file = os.path.join(date_log_dir, f'{name}.log')
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(processName)s - %(process)d - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 使用线程锁确保文件处理器的创建是线程安全的
        with self._lock:
            # 创建文件处理器
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8',
                delay=True  # 延迟文件创建
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)  # 改为INFO级别
            console_handler.setFormatter(formatter)
            
            # 清除现有处理器
            self.logger.handlers.clear()
            
            # 直接添加处理器，不使用队列
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            
            # 允许日志传播到根记录器
            self.logger.propagate = True
            
            # 记录日志目录信息
            self.logger.info(f"日志文件保存在: {date_log_dir}")
    
    def debug(self, message):
        self.logger.debug(message)
    
    def info(self, message):
        self.logger.info(message)
    
    def warning(self, message):
        self.logger.warning(message)
    
    def error(self, message):
        self.logger.error(message)
    
    def critical(self, message):
        self.logger.critical(message)

# 创建各个运营商的日志记录器实例
telecom_logger = Logger(name='telecom', operator='ChinaTelecom').logger
mobile_logger = Logger(name='mobile', operator='ChinaMobile').logger
unicom_logger = Logger(name='unicom', operator='ChinaUnicom').logger
broadnet_logger = Logger(name='broadnet', operator='ChinaBroadnet').logger

# 默认日志记录器
logger = Logger(name='common', operator='common').logger 