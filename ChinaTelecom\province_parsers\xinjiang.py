from playwright.async_api import Page
from common.logger_config import telecom_logger as logger

async def parse_xinjiang_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析新疆资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析新疆{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.p-b-10.w100", timeout=10000)
        
        # 先最大化浏览器窗口
        logger.info("最大化浏览器窗口...")
        await page.evaluate("""() => {
            window.moveTo(0, 0);
            window.resizeTo(screen.availWidth, screen.availHeight);
        }""")
        await page.wait_for_timeout(2000)  # 等待窗口调整完成
        
        # 获取所有产品项
        products = await page.query_selector_all("div.m-t-20.w100")
        
        if not products:
            logger.warning(f"未找到{menu_type}的产品项")
            return
        
        total_products = len(products)
        logger.info(f"找到 {total_products} 个产品项")
        
        # 存储当前类型的产品数据
        products_data = []
        
        logger.info(f"开始解析{menu_type}产品，共{total_products}个")
        
        # 遍历处理每个产品
        for index, product in enumerate(products, 1):
            try:
                logger.info(f"开始解析第 {index}/{total_products} 个产品")
                
                # 获取产品标题和编号
                title_elem = await product.query_selector("div.w100.flex.baseline.between > div.w80.f18.fw_65.ellipsis")
                code_elem = await product.query_selector("div.w100.flex.baseline.between > div.f14.color999")
                
                if not title_elem:
                    logger.error(f"第 {index} 个产品未找到标题元素")
                    continue
                
                title = await title_elem.text_content()
                code = await code_elem.text_content() if code_elem else ""
                code = code.replace("方案编号：", "").strip()
                
                logger.info(f"产品标题: {title}")
                logger.info(f"方案编号: {code}")
                
                # 获取基本信息
                basic_info = {}
                info_rows = await product.query_selector_all("div.el-row.is-align-top")
                for row in info_rows:
                    try:
                        cols = await row.query_selector_all("div.el-col")
                        for i in range(0, len(cols), 2):
                            if i + 1 < len(cols):
                                label = await cols[i].text_content()
                                value = await cols[i + 1].text_content()
                                # 移除冒号
                                label = label.replace("：", "").strip()
                                basic_info[label] = value.strip()
                                logger.info(f"基本信息 - {label}: {value.strip()}")
                    except Exception as e:
                        logger.error(f"解析基本信息行时发生错误: {str(e)}")
                
                # 获取服务内容
                service_content = {}
                table_rows = await product.query_selector_all("div.el-table__body tbody tr")
                for row in table_rows:
                    try:
                        cells = await row.query_selector_all("td div.cell")
                        if len(cells) >= 3:  # 确保有足够的单元格
                            service_content["名称"] = await cells[0].text_content()
                            service_content["资费标准"] = await cells[1].text_content()
                            service_content["适用地区"] = await cells[2].text_content()
                            logger.info(f"服务内容 - 名称: {service_content['名称']}, 资费标准: {service_content['资费标准']}")
                    except Exception as e:
                        logger.error(f"解析服务内容行时发生错误: {str(e)}")
                
                # 获取备注信息
                remark = ""
                remark_elem = await product.query_selector("div.m-t-20.ellipsis-multiple div")
                if remark_elem:
                    remark = await remark_elem.text_content()
                    logger.info(f"备注信息: {remark[:100]}...")  # 只记录前100个字符
                
                # 构建产品数据
                product_data = {
                    "title": title,
                    "code": code,
                    "basic_info": basic_info,
                    "service_content": service_content,
                    "remark": remark
                }
                
                products_data.append(product_data)
                logger.info(f"成功解析第 {index} 个产品: {title}")
                
            except Exception as e:
                logger.error(f"解析第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                continue
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(products_data)}个")
        
        if products_data:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = products_data
            logger.info(f"成功保存{menu_type}的{len(products_data)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_xinjiang_menu_config() -> dict:
    """获取新疆的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.el-tabs__nav-scroll",
        'menu_selector': "div.el-tabs__item",
        'list_selector': "div.p-b-10.w100",
    } 