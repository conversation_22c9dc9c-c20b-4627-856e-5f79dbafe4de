import sys
import os
from pathlib import Path
import asyncio
from typing import Optional, Dict, Any, List
from enum import Enum
import subprocess

# 设置Windows事件循环
if sys.platform == 'win32':
    loop = asyncio.ProactorEventLoop()
    asyncio.set_event_loop(loop)

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 导入其他依赖
from fastapi import FastAPI, HTTPException, BackgroundTasks, status
from pydantic import BaseModel, validator, Field
import uvicorn
import aiohttp
from datetime import datetime
from common.logger_config import logger

# 初始化colorama（如果可用）
try:
    import colorama
    colorama.init()
except ImportError:
    pass

# 设置Windows控制台编码（如果可用）
if sys.platform == 'win32':
    try:
        import ctypes
        kernel32 = ctypes.windll.kernel32
        kernel32.SetConsoleOutputCP(65001)
        kernel32.SetConsoleCP(65001)
    except Exception:
        pass

class CrawlerType(str, Enum):
    """爬虫类型枚举"""
    MOBILE = "mobile"  # 中国移动
    TELECOM = "telecom"  # 中国电信
    UNICOM = "unicom"  # 中国联通
    BROADNET = "broadnet"  # 中国广电

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败

class CrawlerRequest(BaseModel):
    """爬虫请求模型"""
    crawler_type: CrawlerType = Field(..., description="爬虫类型：mobile/telecom/unicom/broadnet")
    provinces: Optional[str] = Field(None, description="省份编码，多个省份用逗号分隔")
    callback_url: Optional[str] = Field(None, description="回调URL")
    timeout: Optional[int] = Field(300, description="超时时间（秒）")
    retry_count: Optional[int] = Field(3, description="重试次数")

    @validator('provinces')
    def validate_provinces(cls, v):
        """验证并标准化provinces参数"""
        if v is not None and not isinstance(v, str):
            raise ValueError("provinces必须是字符串")
        return v

    @validator('timeout')
    def validate_timeout(cls, v):
        """验证超时时间"""
        if v is not None and (v < 60 or v > 3600):
            raise ValueError("timeout必须在60-3600秒之间")
        return v

    @validator('retry_count')
    def validate_retry_count(cls, v):
        """验证重试次数"""
        if v is not None and (v < 0 or v > 5):
            raise ValueError("retry_count必须在0-5之间")
        return v

class TaskInfo(BaseModel):
    """任务信息模型"""
    task_id: str
    status: TaskStatus
    message: str
    start_time: datetime
    end_time: Optional[datetime] = None
    zip_file: Optional[str] = None
    error: Optional[str] = None

class CrawlerResponse(BaseModel):
    """爬虫响应模型"""
    task_id: str
    status: str
    message: str

# 存储任务状态
tasks: Dict[str, TaskInfo] = {}

# 爬虫URL配置
CRAWLER_URLS = {
    CrawlerType.MOBILE: "https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov={}",
    CrawlerType.TELECOM: "https://www.189.cn/jtzfzq",
    CrawlerType.UNICOM: "https://img.client.10010.com/zifeizhuanquwt/index.html?time=1747278766317#/home?procode={}",
    CrawlerType.BROADNET: "https://m.10099.com.cn/expensesNotice/#/home?codeValue=ERVUnSIW21q14Wrc56CBrJLXuWJRVP%2FWTOs39msNqxt6lsifUBhKgqKgPkAbsTsghbuTK%2BA5g8dxTMIT0GUpTqO0D0AwNNH6XVP6Tbx%2FWVsdeGn5BgL8xOVm0B3%2F73uBasyFV%2FXjlgjbtYRlaBBsqWAiZPjdVTFSVJbxK%2FveO5HH78XsbBgxqsfpoF1fTH35CUWY7tXPK5tI6EvCd8%2BCYfjw48b3LoaVp3FioFo5Lts%2Fz7tRCEquXIrtzMSIjQ%2BttVN4xuY0wL399stYVzwu%2BYyj5yda0e4NC4qxs0pp3tvhXrRicUV9SmmSBf5ZpFbVrTe%2F26p%2FhxIj7QuHfbAwMw%3D%3D"
}

app = FastAPI(
    title="运营商资费数据采集API",
    description="提供统一的接口来采集各运营商资费数据",
    version="1.0.0"
)

async def run_crawler(crawler_type: CrawlerType, provinces: Optional[str], callback_url: Optional[str], task_id: str) -> None:
    """运行爬虫任务
    
    Args:
        crawler_type: 爬虫类型
        provinces: 省份编码
        callback_url: 回调URL
        task_id: 任务ID
    """
    try:
        # 更新任务状态为运行中
        tasks[task_id].status = TaskStatus.RUNNING
        tasks[task_id].message = "任务正在执行"
        
        # 根据爬虫类型选择对应的爬虫
        if crawler_type == CrawlerType.UNICOM:
            from ChinaUnicom.unicom_crawler import main as crawler_main
        elif crawler_type == CrawlerType.MOBILE:
            from ChinaMobile.mobile_crawler import main as crawler_main
        elif crawler_type == CrawlerType.BROADNET:
            from ChinaBroadnet.broadnet_crawler import main as crawler_main
        else:
            raise ValueError(f"不支持的爬虫类型: {crawler_type}")

        # 运行爬虫
        _ = await crawler_main(provinces,callback_url)

    except Exception as e:
        # 更新任务id].error = str(e)
        logger.error(f"执行爬虫任务时发生错误: {str(e)}")
        raise

@app.post("/api/v1/crawl", response_model=CrawlerResponse)
async def start_crawler(request: CrawlerRequest, background_tasks: BackgroundTasks):
    """启动爬虫任务"""
    try:
        # 生成任务ID
        task_id = f"{request.crawler_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # 创建任务信息
        tasks[task_id] = TaskInfo(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="任务等待执行",
            start_time=datetime.now()
        )
        
        # 如果是电信爬虫，直接执行
        if request.crawler_type == CrawlerType.TELECOM:
            try:
                # 更新任务状态为运行中
                tasks[task_id].status = TaskStatus.RUNNING
                tasks[task_id].message = "任务正在执行"
                
                # 构建命令行参数
                cmd = [sys.executable, "..\\ChinaTelecom\\telecom_crawler.py"]
                
                # 添加参数
                if request.provinces:
                    cmd.extend(["--provinces", request.provinces])
                if request.callback_url:
                    cmd.extend(["--callback-url", request.callback_url])
                    
                # 启动进程但不等待完成
                subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace'
                )
                
                # 立即返回响应
                return CrawlerResponse(
                    task_id=task_id,
                    status="accepted",
                    message="电信爬虫任务已启动"
                )
                    
            except Exception as e:
                # 更新任务状态为失败
                tasks[task_id].status = TaskStatus.FAILED
                tasks[task_id].message = f"任务执行失败: {str(e)}"
                tasks[task_id].end_time = datetime.now()
                tasks[task_id].error = str(e)
                logger.error(f"启动电信爬虫任务时发生错误: {str(e)}")
                raise
        else:
            # 其他爬虫使用后台任务
            background_tasks.add_task(
                    run_crawler,
                request.crawler_type,
                request.provinces,
                    request.callback_url,
                    task_id
            )
        
        return CrawlerResponse(
            task_id=task_id,
            status="accepted",
            message="任务已接受"
        )
        
    except Exception as e:
        logger.error(f"启动爬虫任务时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/api/v1/task/{task_id}", response_model=TaskInfo)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )
    return tasks[task_id]

@app.get("/api/v1/tasks", response_model=List[TaskInfo])
async def list_tasks():
    """获取所有任务状态"""
    return list(tasks.values())

if __name__ == "__main__":
    # 设置Windows事件循环
    if sys.platform == 'win32':
        loop = asyncio.ProactorEventLoop()
        asyncio.set_event_loop(loop)
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8123,
        reload=True,
        log_level="debug",
        access_log=True
    ) 