from playwright.async_api import Page
from common.logger_config import telecom_logger as logger
import os
import random
from datetime import datetime

async def save_page_html(page: Page, menu_type: str, page_num: int) -> None:
    """保存页面HTML结构到文件
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        page_num: 页码
    """
    try:
        # 等待页面完全加载
        await page.wait_for_load_state('networkidle', timeout=10000)
        await page.wait_for_timeout(2000)  # 额外等待时间
        
        # 获取页面HTML内容
        html_content = await page.content()
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d')
        random_num = random.randint(1000, 9999)
        
        # 构建文件名：云南_yyyymmdd_随机数_菜单类型_第X页.html
        menu_type_sanitized = menu_type.replace('/', '_').replace('\\', '_').replace(':', '_')
        filename = f"云南_{timestamp}_{random_num}_{menu_type_sanitized}_第{page_num}页.html"
        
        # 确保目录存在
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "yn")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        
        filepath = os.path.join(data_dir, filename)
        
        # 保存HTML内容
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"第{page_num}页HTML结构已保存到: {filename}")
        
    except Exception as e:
        logger.error(f"保存第{page_num}页HTML结构时发生错误: {str(e)}")

async def parse_yunnan_content(page: Page, menu_type: str, all_products_data: dict) -> None:
    """解析云南资费页面内容
    
    Args:
        page: Playwright页面对象
        menu_type: 菜单类型
        all_products_data: 存储所有产品数据的字典
    """
    try:
        logger.info(f"开始解析云南{menu_type}的页面内容...")
        
        # 等待列表区域加载
        logger.info("等待列表区域加载...")
        await page.wait_for_selector("div.tc_content", timeout=10000)
        
        # 先最大化浏览器窗口
        logger.info("最大化浏览器窗口...")
        await page.evaluate("""() => {
            window.moveTo(0, 0);
            window.resizeTo(screen.availWidth, screen.availHeight);
        }""")
        await page.wait_for_timeout(2000)  # 等待窗口调整完成
        
        # 存储所有产品数据
        all_products = []
        current_page = 1
        has_next_page = True
        
        while has_next_page:
            logger.info(f"开始解析第 {current_page} 页...")
            
            # 保存当前页的HTML结构
            await save_page_html(page, menu_type, current_page)
            
            # 获取当前页的产品项
            products = await page.query_selector_all("div.list-box > div")
            
            if not products:
                logger.warning(f"第 {current_page} 页未找到产品项")
                break
            
            total_products = len(products)
            logger.info(f"第 {current_page} 页找到 {total_products} 个产品项")
            
            # 遍历处理每个产品
            for index, product in enumerate(products, 1):
                try:
                    logger.info(f"开始解析第 {current_page} 页第 {index}/{total_products} 个产品")
                    
                    # 获取产品标题和编号
                    title_elem = await product.query_selector("div.top.clearfix > p.fl.title")
                    code_elem = await product.query_selector("div.top.clearfix > p.fl.small")
                    
                    if not title_elem or not code_elem:
                        logger.error(f"第 {current_page} 页第 {index} 个产品未找到标题或编号元素")
                        continue
                    
                    title = await title_elem.text_content()
                    code = await code_elem.text_content()
                    
                    logger.info(f"产品标题: {title}")
                    logger.info(f"产品编号: {code}")
                    
                    # 提取方案编号，删除空格
                    if "方案编号" in code:
                        code = code.split("方案编号")[1].strip()
                        code = code.replace("：", "").strip()
                    code = code.replace(" ", "")
                    
                    # 获取基本信息
                    basic_info = {}
                    info_div = await product.query_selector("div.text > div.clearfix")
                    if info_div:
                        info_items = await info_div.query_selector_all("div > p")
                        for item in info_items:
                            text = await item.text_content()
                            if "：" in text:
                                key, value = text.split("：", 1)
                                key = key.replace("<b>", "").replace("</b>", "").strip()
                                value = value.replace("<b>", "").replace("</b>", "").strip()
                                basic_info[key] = value
                    
                    logger.info(f"基本信息: {basic_info}")
                    
                    # 获取服务内容
                    service_content = {}
                    table = await product.query_selector("div.table-out table")
                    if table:
                        rows = await table.query_selector_all("tr")
                        if len(rows) >= 2:  # 确保有表头和内容行
                            headers = await rows[0].query_selector_all("th")
                            values = await rows[1].query_selector_all("td")
                            for i in range(min(len(headers), len(values))):
                                key = await headers[i].text_content()
                                value = await values[i].text_content()
                                service_content[key.strip()] = value.strip()
                    
                    logger.info(f"服务内容: {service_content}")
                    
                    # 获取备注信息
                    remark = ""
                    remark_div = await product.query_selector("div.remark > div")
                    if remark_div:
                        remark = await remark_div.text_content()
                        logger.info(f"备注信息: {remark[:100]}...")  # 只记录前100个字符
                    
                    # 构建产品数据
                    product_data = {
                        "title": title,
                        "code": code,
                        "basic_info": basic_info,
                        "service_content": service_content,
                        "remark": remark
                    }
                    
                    all_products.append(product_data)
                    logger.info(f"成功解析第 {current_page} 页第 {index} 个产品: {title}")
                    
                except Exception as e:
                    logger.error(f"解析第 {current_page} 页第 {index}/{total_products} 个产品时发生错误: {str(e)}")
                    continue
            
            # 检查是否有下一页
            # 先滚动到页面底部
            logger.info("滚动到页面底部...")
            await page.evaluate("""() => {
                window.scrollTo(0, document.body.scrollHeight);
            }""")
            await page.wait_for_timeout(1000)  # 等待滚动完成
            
            # 获取当前页码和总页数
            try:
                # 等待页码信息加载
                page_info = await page.wait_for_selector("span:has-text('当前页:')", timeout=5000)
                if not page_info:
                    logger.warning("未找到页码信息元素")
                    has_next_page = False
                    continue
                    
                page_text = await page_info.text_content()
                logger.info(f"获取到的页码信息: {page_text}")
                
                if not page_text or page_text == "-":
                    logger.warning("页码信息未正确加载，等待重试...")
                    await page.wait_for_timeout(2000)  # 等待重试
                    continue
                
                # 解析页码信息，格式为 "当前页:5 / 11"
                current_page_num = int(page_text.split("当前页:")[1].split("/")[0].strip())
                total_pages = int(page_text.split("/")[1].strip())
                logger.info(f"当前第 {current_page_num} 页，共 {total_pages} 页")
                
                if current_page_num >= total_pages:
                    logger.info("已到达最后一页，解析完成")
                    has_next_page = False
                    continue
                
                # 查找并点击下一页按钮
                next_button = await page.query_selector("button:text('下一页')")
                if not next_button:
                    logger.warning("未找到下一页按钮")
                    has_next_page = False
                    continue
                    
                # 点击下一页按钮
                logger.info(f"点击下一页按钮，准备加载第 {current_page_num + 1} 页")
                await next_button.click()
                await page.wait_for_timeout(2000)  # 等待新页面加载
                
                # 等待列表区域重新加载
                try:
                    await page.wait_for_selector("div.tc_content", timeout=10000)
                    logger.info("新页面加载完成")
                    current_page += 1
                except Exception as e:
                    logger.error(f"等待新页面加载超时: {str(e)}")
                    has_next_page = False
            except Exception as e:
                logger.error(f"处理页码信息时发生错误: {str(e)}")
                has_next_page = False
        
        logger.info(f"完成解析{menu_type}产品，成功解析{len(all_products)}个")
        
        if all_products:
            # 将数据存储到总数据中
            all_products_data["local"][menu_type] = all_products
            logger.info(f"成功保存{menu_type}的{len(all_products)}个产品数据")
        else:
            logger.warning(f"{menu_type}没有解析到任何产品数据")
        
    except Exception as e:
        logger.error(f"解析页面内容时发生错误: {str(e)}")
        raise

def get_yunnan_menu_config() -> dict:
    """获取云南的菜单配置
    
    Returns:
        dict: 菜单配置
    """
    return {
        'wait_selector': "div.menu-box",
        'menu_selector': "div.top.clearfix > div",
        'list_selector': "div.tc_content"
    } 